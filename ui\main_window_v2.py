#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 主窗口界面 - 雷电模拟器中控系统
========================================
功能描述: 主窗口界面，完全复刻原有界面的外观和功能
主要方法: init_ui, create_nav_panel, create_home_page, create_settings_page
调用关系: 被main.py调用，调用其他UI组件
注意事项: 代码简洁、结构清晰、层级分明、易于维护
========================================
"""

# ========================================
# 🎯 主窗口核心功能组
# ========================================
# 功能描述: 主窗口界面的创建、初始化和核心功能管理
# 主要方法: __init__(), init_ui(), create_nav_panel(), create_home_page()
# 调用关系: 程序入口，被main()调用，调用各个子模块
# 注意事项: UI层只负责界面展示，业务逻辑通过事件总线处理
# 架构合规: 严格遵循分层架构，UI层不直接处理业务逻辑
# ========================================

from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                           QStackedWidget, QSplitter, QTableView, QTextEdit,
                           QLabel, QProgressBar, QGridLayout, QFrame, QHeaderView, QApplication,
                           QMenu, QMessageBox, QDialog, QSpinBox, QAbstractItemView, QTabWidget)
from PyQt6.QtCore import Qt, pyqtSignal, QAbstractTableModel, QModelIndex
from PyQt6.QtGui import QFont, QColor, QAction

# 🎯 修复架构违规：使用UI服务层而不是直接导入core层
from ui.ui_service_layer import get_ui_service_layer


# 导入自定义UI组件
from .styled_widgets import (StyledButton, StyledLineEdit, GradientFrame,
                           DonutWidget, NavButton)

# ========================================
# 🎯 现代化自定义输入对话框
# ========================================

class ModernInputDialog(QDialog):
    """现代优雅的自定义输入对话框"""

    def __init__(self, parent=None, title="输入", message="请输入数值:",
                 value=1, min_val=1, max_val=9999):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setModal(True)
        self.setFixedSize(380, 200)

        # 设置窗口标志，移除问号按钮
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.WindowCloseButtonHint)

        self.init_ui(message, value, min_val, max_val)
        self.apply_modern_style()

    def init_ui(self, message, value, min_val, max_val):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(30, 25, 30, 25)
        layout.setSpacing(20)

        # 消息标签
        self.message_label = QLabel(message)
        self.message_label.setWordWrap(True)
        self.message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.message_label)

        # 输入框容器
        input_container = QWidget()
        input_layout = QHBoxLayout(input_container)
        input_layout.setContentsMargins(0, 0, 0, 0)
        input_layout.setSpacing(10)

        # 输入框
        self.spin_box = QSpinBox()
        self.spin_box.setMinimum(min_val)
        self.spin_box.setMaximum(max_val)
        self.spin_box.setValue(value)
        self.spin_box.setFixedHeight(40)
        self.spin_box.setAlignment(Qt.AlignmentFlag.AlignCenter)

        input_layout.addStretch()
        input_layout.addWidget(self.spin_box)
        input_layout.addStretch()

        layout.addWidget(input_container)

        # 按钮容器
        button_container = QWidget()
        button_layout = QHBoxLayout(button_container)
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(15)

        # 取消按钮
        self.cancel_btn = StyledButton("取消")
        self.cancel_btn.setFixedSize(100, 35)
        self.cancel_btn.clicked.connect(self.reject)

        # 确定按钮
        self.ok_btn = StyledButton("确定")
        self.ok_btn.setFixedSize(100, 35)
        self.ok_btn.clicked.connect(self.accept)
        self.ok_btn.setDefault(True)

        button_layout.addStretch()
        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.ok_btn)
        button_layout.addStretch()

        layout.addWidget(button_container)

        # 设置焦点到输入框
        self.spin_box.setFocus()
        self.spin_box.selectAll()

    def apply_modern_style(self):
        """应用现代化样式"""
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border: 1px solid #e0e0e0;
                border-radius: 12px;
            }
            QLabel {
                font-size: 14px;
                color: #333;
                font-weight: 500;
                line-height: 1.4;
            }
            QSpinBox {
                background-color: white;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 16px;
                font-weight: 600;
                color: #333;
                min-width: 120px;
            }
            QSpinBox:focus {
                border: 2px solid #FF9800;
                background-color: #fff8f0;
            }
            QSpinBox:hover {
                border: 2px solid #FFB74D;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                background-color: #f5f5f5;
                border: none;
                border-radius: 4px;
                width: 20px;
                height: 15px;
            }
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {
                background-color: #FF9800;
            }
            QSpinBox::up-arrow, QSpinBox::down-arrow {
                width: 8px;
                height: 8px;
            }
        """)

    def get_value(self):
        """获取输入值"""
        return self.spin_box.value()

    @staticmethod
    def get_int(parent, title, message, value=1, min_val=1, max_val=9999):
        """静态方法，类似QInputDialog.getInt"""
        dialog = ModernInputDialog(parent, title, message, value, min_val, max_val)
        result = dialog.exec()
        if result == QDialog.DialogCode.Accepted:
            return dialog.get_value(), True
        else:
            return 0, False


class MainWindowV2(QMainWindow):
    """
    雷电模拟器中控系统主窗口
    ========================================
    功能描述: 系统主界面，集成模拟器管理、任务控制、状态监控
    主要功能:
    - 模拟器列表：显示、选择、批量操作（启动/停止）
    - 任务管理：Instagram私信等任务配置和执行
    - 实时监控：模拟器状态、任务进度、系统统计
    - 配置管理：与统一配置系统和异步桥梁集成
    ========================================
    """

    # 🎯 信号定义 - 组件间通信
    emulator_path_changed = pyqtSignal(str)  # 模拟器路径变更信号
    refresh_requested = pyqtSignal()         # 刷新请求信号
    settings_requested = pyqtSignal()        # 设置页面请求信号

    # 异步通信信号
    batch_operation_requested = pyqtSignal(str, object)  # 操作类型，数据

    # ============================================================================
    # 🎯 1. 业务层操作结果处理模块
    # ============================================================================
    # 功能描述: 处理异步桥梁返回的各种操作结果，统一更新UI状态和显示
    # 调用关系: 被异步桥梁通过信号调用，处理扫描、启动、停止等操作结果
    # 注意事项: 统一处理所有操作类型，确保UI状态同步和错误处理
    # ============================================================================

    def on_operation_completed(self, operation_type: str, result):
        """处理业务层操作完成信号 - 修复版，统一处理所有操作"""
        try:
            # 🎯 修复架构违规：使用UI服务层

            if operation_type == "scan_emulators":
                # 处理模拟器扫描结果
                if isinstance(result, list) and len(result) > 0:
                    self.emulator_list = result
                    self._show_log(f"扫描完成: 找到 {len(self.emulator_list)} 个模拟器")

                    # 立即更新UI显示
                    self._populate_table_data()

                    # 记录日志
                    self.ui_service.log_emulator(f"模拟器扫描完成", count=len(self.emulator_list), component="MainWindowV2")

                    # 🎯 确保UI完全更新后再处理后续状态变化
                    if hasattr(self, 'table_view'):
                        self.table_view.viewport().update()
                        # 删除伪异步处理 - 表格渲染通过Qt信号自动完成

                elif isinstance(result, list) and len(result) == 0:
                    self.emulator_list = []
                    self._show_log("扫描完成: 未找到模拟器")
                    self._populate_table_data()
                else:
                    self.emulator_list = []
                    self._show_log("模拟器扫描失败: 数据格式错误")
                    self._populate_table_data()

            elif operation_type == 'start_batch':
                # 处理批量启动结果
                if isinstance(result, dict):
                    success = result.get('success', 0)
                    total = result.get('total', 0)
                    self._show_log(f"批量启动完成: {success}/{total} 成功")
                    self.ui_service.log_emulator(f"批量启动操作完成", success=success, total=total, component="MainWindowV2")

            elif operation_type == 'stop_batch':
                # 处理批量停止结果
                if isinstance(result, dict):
                    success = result.get('success', 0)
                    total = result.get('total', 0)
                    self._show_log(f"批量停止完成: {success}/{total} 成功")
                    self.ui_service.log_emulator(f"批量停止操作完成", success=success, total=total, component="MainWindowV2")

            elif operation_type == 'health_check':
                # 处理健康检查结果
                if isinstance(result, dict):
                    healthy = result.get('healthy', 0)
                    total = result.get('total', 0)
                    self._show_log(f"健康检查: {healthy}/{total} 健康")
                    self.ui_service.log_performance("健康检查完成", 0.1, healthy=healthy, total=total, component="HealthMonitoring")

            elif operation_type == 'close_all_tasks':
                # 关闭所有任务完成
                if isinstance(result, dict):
                    status = result.get('status', 'unknown')
                    message = result.get('message', '')
                    if status == 'success':
                        self._show_log(f"所有任务和模拟器已关闭: {message}")
                        # 🎯 批量更新UI状态 - 将所有模拟器状态设为已停止
                        self._batch_update_all_emulators_stopped()
                    else:
                        self._show_log(f"关闭所有任务失败: {message}")

            elif operation_type == 'instagram_dm_task':
                # 🎯 Instagram粉丝私信任务操作完成
                if isinstance(result, dict):
                    status = result.get('status', 'unknown')
                    message = result.get('message', '未知结果')
                    emulator_count = result.get('emulator_count', 0)

                    if status == 'started':
                        self._show_log(f"Instagram粉丝私信任务启动成功: {message}")
                    elif status == 'failed':
                        self._show_log(f"Instagram粉丝私信任务启动失败: {message}")
                    else:
                        self._show_log(f"Instagram粉丝私信任务状态更新: {message}")

        except Exception as e:
            self._show_log(f"处理操作结果失败: {e}")

    def _batch_update_all_emulators_stopped(self):
        """批量更新所有模拟器状态为已停止 - 通过业务层统一更新"""
        try:
            if hasattr(self, 'table_model') and self.table_model:
                # 🎯 收集所有模拟器ID
                emulator_ids = []
                for row in range(len(self.table_model.emulators)):
                    emulator_id = self.table_model.emulators[row][1]  # ID列
                    emulator_ids.append(emulator_id)

                # 🎯 通过业务层批量更新，而不是直接操作表格数据
                if emulator_ids:
                    from core.status_converter import batch_update_emulators
                    updates = [{'id': eid, 'status': '已停止'} for eid in emulator_ids]
                    changed_rows = batch_update_emulators(updates, table_model=self.table_model)

                    if changed_rows:
                        self._show_log(f"已批量更新 {len(emulator_ids)} 个模拟器状态为已停止")
                    else:
                        self.logger.warning("批量更新未产生任何变化")

        except Exception as e:
            self.logger.error(f"批量更新模拟器状态失败: {e}")

    def on_operation_failed(self, operation_type: str, error_msg: str):
        """处理业务层操作失败信号"""
        self._show_log(f"{operation_type} 操作失败: {error_msg}")
        if operation_type == "scan_emulators":
            self.emulator_list = []
            self._populate_table_data()

    def on_operation_progress(self, operation_type: str, current: int, total: int):
        """处理业务层操作进度信号"""
        if total > 0:
            progress = int((current / total) * 100)
            self._show_log(f"{operation_type}: {current}/{total} ({progress}%)")
        else:
            self._show_log(f"{operation_type}: {current}/{total}")

    def on_emulator_status_changed(self, emulator_id: int, old_status: str, new_status: str):
        """处理单个模拟器状态变化 - 使用统一的UI更新管理器"""
        try:
            self.logger.debug(f"模拟器{emulator_id}状态变化: {old_status} -> {new_status}")

            # 🎯 使用统一的状态更新接口
            from core.status_converter import update_emulator_status
            success = update_emulator_status(emulator_id, new_status, table_model=self.emulator_model)

            if not success:
                self.logger.warning(f"未找到模拟器{emulator_id}，无法更新状态")

            # 更新统计信息
            self._update_statistics()

            # 🎯 单个模拟器状态变化日志 - 保持现有逻辑
            self._show_log(f"模拟器{emulator_id}: {old_status} -> {new_status}")

        except Exception as e:
            self.logger.error(f"处理状态变化失败: {e}")

    def on_batch_status_changed(self, emulator_ids: list, old_status: str, new_status: str):
        """🎯 处理批量状态变化 - 事件驱动汇总日志，避免重复记录"""
        try:
            count = len(emulator_ids)
            if count > 1:
                # 多个模拟器相同变化，汇总记录
                self._show_log(f"{count}个模拟器: {old_status} -> {new_status}")
            elif count == 1:
                # 单个模拟器变化，正常记录
                self._show_log(f"模拟器{emulator_ids[0]}: {old_status} -> {new_status}")

            # 批量更新UI状态
            for emulator_id in emulator_ids:
                from core.status_converter import update_emulator_status
                success = update_emulator_status(emulator_id, new_status, table_model=self.emulator_model)
                if not success:
                    self.logger.warning(f"未找到模拟器{emulator_id}，无法更新状态")

            # 更新统计信息
            self._update_statistics()

        except Exception as e:
            self.logger.error(f"处理批量状态变化失败: {e}")

    def on_emulator_status_changed_detailed(self, emulator_id: int, old_status: str, new_status: str, detailed_info: dict):
        """🎯 处理包含详细信息的模拟器状态变化 - 使用统一的UI更新管理器"""
        try:
            # 🎯 修复架构违规：使用UI服务层
            from datetime import datetime

            self.ui_service.log_info(f"模拟器{emulator_id}: {old_status} -> {new_status}, PID: {detailed_info.get('pid', 'N/A')}", component="MainWindowV2")

            # 🎯 使用统一的详细状态更新接口
            from core.status_converter import update_emulator_detailed

            # 准备完整的更新数据
            update_data = {
                'pid': detailed_info.get('pid', -1),
                'top_hwnd': detailed_info.get('top_hwnd', detailed_info.get('top_handle', -1)),
                'bind_hwnd': detailed_info.get('bind_hwnd', detailed_info.get('bind_handle', -1)),
                'adb_port': detailed_info.get('adb_port', -1),
                'startup_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S") if new_status == '运行中' else None
            }

            success = update_emulator_detailed(emulator_id, new_status, update_data, table_model=self.emulator_model)
            if not success:
                self.logger.warning(f"未找到模拟器{emulator_id}，无法更新详细状态")

            # 更新统计信息
            self._update_statistics()

            # 显示状态变化日志 - 业务层已返回中文状态
            self._show_log(f"模拟器{emulator_id}: {old_status} -> {new_status}")

        except Exception as e:
            self.logger.error(f"处理详细模拟器状态变化失败: {e}")
            import traceback
            traceback.print_exc()

    def on_startup_progress_updated(self, progress_info: dict):
        """🎯 处理启动进度更新"""
        try:
            # 显示详细的启动进度信息
            total = progress_info.get('total', 0)
            queued = progress_info.get('queued', 0)
            starting = progress_info.get('starting', 0)
            running = progress_info.get('running', 0)
            failed = progress_info.get('failed', 0)
            cancelled = progress_info.get('cancelled', 0)
            concurrent_used = progress_info.get('concurrent_slots_used', 0)
            max_concurrent = progress_info.get('max_concurrent', 2)

            # 更新日志显示
            if total > 0:
                progress_msg = f"启动进度: 排队{queued}个, 启动中{starting}个, 运行{running}个, 失败{failed}个 (并发槽位:{concurrent_used}/{max_concurrent})"
                self._show_log(progress_msg)

            # 更新统计信息
            self._update_statistics()

        except Exception as e:
            self.logger.error(f"处理启动进度更新失败: {e}")

    def on_emulator_batch_updated(self, updates: list):
        """处理批量状态更新 - 使用统一的UI更新管理器"""
        try:
            self.logger.debug(f"批量状态更新: {len(updates)}个模拟器")

            # 🎯 使用统一的批量更新接口
            from core.status_converter import batch_update_emulators
            changed_rows = batch_update_emulators(updates, table_model=self.emulator_model)

            # 更新统计信息
            self._update_statistics()

            # 显示批量更新日志
            self._show_log(f"批量更新: {len(updates)}个模拟器状态已更新，影响{len(changed_rows)}行")

        except Exception as e:
            self.logger.error(f"处理批量状态更新失败: {e}")

    # 🎯 删除重复的状态更新方法，统一使用表格模型更新

    # ============================================================================
    # 🎯 6. 心跳监控信号处理模块
    # ============================================================================
    # 功能描述: 处理心跳监控系统发送的各种信号，更新UI状态显示
    # 调用关系: 被心跳监控系统通过信号调用，处理成功、失败、恢复等事件
    # 注意事项: 通过业务层统一更新，避免直接操作表格数据
    # ============================================================================

    def on_heartbeat_success(self, emulator_id: int):
        """🎯 处理心跳检测成功信号"""
        try:
            self.logger.debug(f"模拟器{emulator_id}心跳检测正常")
            # 🎯 更新UI中的心跳状态显示
            self._update_heartbeat_status_in_ui(emulator_id)

        except Exception as e:
            self.logger.error(f"处理心跳成功信号失败: {e}")

    def on_heartbeat_failed(self, emulator_id: int, error_msg: str, failure_count: int):
        """🎯 处理心跳检测失败信号"""
        try:
            self._show_log(f"模拟器{emulator_id}心跳异常: {error_msg} (失败次数: {failure_count})")
            self.logger.warning(f"模拟器{emulator_id}心跳检测失败: {error_msg}, 失败次数: {failure_count}")
            # 🎯 更新UI中的心跳状态显示
            self._update_heartbeat_status_in_ui(emulator_id)

        except Exception as e:
            self.logger.error(f"处理心跳失败信号失败: {e}")

    def _update_heartbeat_status_in_ui(self, emulator_id: int):
        """🎯 触发心跳状态更新 - 通过业务层统一更新，避免直接操作表格"""
        try:
            # 🎯 通过业务层触发心跳状态更新，而不是直接操作表格数据
            # 心跳状态应该由心跳监控系统自动管理，UI层只需要触发更新
            from core.status_converter import get_ui_update_manager
            ui_manager = get_ui_update_manager(self.emulator_model)

            # 找到模拟器当前状态
            row = ui_manager._find_emulator_row(emulator_id)
            if row != -1:
                current_status = self.emulator_model.emulators[row][3]  # 获取当前模拟器状态

                # 🎯 通过统一接口更新，让业务层处理心跳状态的正确设置
                success = ui_manager.update_table_row_status(emulator_id, current_status)

                if success:
                    self.logger.debug(f"已触发模拟器{emulator_id}的心跳状态更新")
                else:
                    self.logger.warning(f"模拟器{emulator_id}心跳状态更新未产生变化")
            else:
                self.logger.warning(f"未找到模拟器{emulator_id}的表格行，无法更新心跳状态")

        except Exception as e:
            self.logger.error(f"触发心跳状态更新失败: {e}")

    def on_emulator_recovered(self, emulator_id: int):
        """🎯 处理模拟器恢复信号"""
        try:
            self._show_log(f"模拟器{emulator_id}已自动恢复正常")
            self.logger.info(f"模拟器{emulator_id}已恢复正常")
            # 🎯 更新UI中的心跳状态显示
            self._update_heartbeat_status_in_ui(emulator_id)

        except Exception as e:
            self.logger.error(f"处理模拟器恢复信号失败: {e}")

    def on_emulator_switched(self, old_emulator_id: int, new_emulator_id: int):
        """🎯 处理模拟器切换信号"""
        try:
            self._show_log(f"模拟器自动切换: {old_emulator_id} -> {new_emulator_id}")
            self.logger.info(f"模拟器已自动切换: {old_emulator_id} -> {new_emulator_id}")

        except Exception as e:
            self.logger.error(f"处理模拟器切换信号失败: {e}")

    def on_task_finished(self, emulator_id: int, row: int, task_name: str, task_args: dict):
        """🎯 参考代码的任务完成处理方式 - 统一状态更新"""
        try:
            self.logger.info(f"任务完成: 模拟器{emulator_id}, 任务{task_name}")

            # 获取任务结果
            result = task_args.get('result', '')
            message = task_args.get('message', '')

            # 🎯 统一使用表格模型更新状态，删除重复逻辑
            if hasattr(self, 'table_model') and self.table_model:
                if task_name == 'start':
                    if '成功' in result:
                        # 启动成功，更新为运行中状态
                        self.table_model.update_single_emulator_status(emulator_id, '运行中')
                        self._show_log(f"模拟器{emulator_id}启动成功")
                    else:
                        # 启动失败，更新为停止状态
                        self.table_model.update_single_emulator_status(emulator_id, '已停止')
                        self._show_log(f"模拟器{emulator_id}启动失败: {message}")

                elif task_name == 'stop':
                    # 停止任务，无论成功失败都更新为停止状态
                    self.table_model.update_single_emulator_status(emulator_id, '已停止')
                    if '成功' in result:
                        self._show_log(f"模拟器{emulator_id}停止成功")
                    else:
                        self._show_log(f"模拟器{emulator_id}停止失败: {message}")

                # 🎯 表格模型会自动处理统计更新，无需重复调用

        except Exception as e:
            self.logger.error(f"处理任务完成信号失败: {e}")

    def get_selected_emulator_ids(self):
        """获取选中的模拟器ID列表"""
        selected_ids = []
        if hasattr(self, 'table_model') and self.table_model:
            for row in range(self.table_model.rowCount()):
                # 检查第一列（选择框）是否被选中
                index = self.table_model.index(row, 0)
                if self.table_model.data(index, Qt.ItemDataRole.CheckStateRole) == Qt.CheckState.Checked:
                    # 获取第二列（模拟器ID）
                    id_index = self.table_model.index(row, 1)
                    emulator_id = self.table_model.data(id_index, Qt.ItemDataRole.DisplayRole)
                    if emulator_id is not None:
                        selected_ids.append(int(emulator_id))
        return selected_ids

    def on_start_selected(self):
        """启动选中的模拟器"""
        # 🎯 修复架构违规：使用UI服务层
        selected_ids = self.get_selected_emulator_ids()
        if selected_ids:
            self.batch_operation_requested.emit('start_batch', selected_ids)
            self._show_log(f"请求启动 {len(selected_ids)} 个模拟器")
            self.ui_service.log_emulator(f"用户请求启动 {len(selected_ids)} 个模拟器", component="MainWindowV2")
        else:
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.information(self, "提示", "请先勾选要启动的模拟器")

    def on_stop_selected(self):
        """停止选中的模拟器"""
        # 🎯 修复架构违规：使用UI服务层
        selected_ids = self.get_selected_emulator_ids()
        if selected_ids:
            self.batch_operation_requested.emit('stop_batch', selected_ids)
            self._show_log(f"请求停止 {len(selected_ids)} 个模拟器")
            self.ui_service.log_emulator(f"用户请求停止 {len(selected_ids)} 个模拟器", component="MainWindowV2")
        else:
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.information(self, "提示", "请先勾选要停止的模拟器")

    def on_arrange_windows(self):
        """手动排列窗口"""
        try:
            self._show_log("手动触发窗口排列")
            # 🎯 使用新的异步桥梁操作
            if hasattr(self, 'async_bridge') and self.async_bridge:
                self.async_bridge.execute_operation('arrange_windows', None)
            else:
                from PyQt6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "警告", "异步桥梁未初始化，无法排列窗口")
        except Exception as e:
            self._show_log(f"手动排列窗口失败: {e}", "error")

    def on_close_all_tasks(self):
        """关闭所有任务"""
        from PyQt6.QtWidgets import QMessageBox

        # 确认对话框
        reply = QMessageBox.question(
            self,
            "确认操作",
            "确定要关闭所有任务和模拟器吗？\n这将：\n• 停止所有任务队列\n• 停止心跳监控\n• 关闭所有运行中的模拟器",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.batch_operation_requested.emit('close_all_tasks', None)
            self._show_log("请求关闭所有任务和模拟器")
            if hasattr(self, 'ui_service') and self.ui_service:
                self.ui_service.log_emulator("用户请求关闭所有任务和模拟器", component="MainWindowV2")

    def on_instagram_dm_task(self):
        """Instagram粉丝私信任务"""
        from PyQt6.QtWidgets import QMessageBox

        try:
            # 获取选中的模拟器
            selected_emulators = self.get_selected_emulators()

            if not selected_emulators:
                QMessageBox.warning(self, "警告", "请先选择要执行任务的模拟器")
                return

            # 准备任务数据
            task_data = {
                'emulators': selected_emulators,
                'task_type': 'instagram_dm',
                'timestamp': self._get_current_timestamp()
            }

            # 🎯 通过异步桥梁执行任务，确保异步执行和统一验证逻辑
            if hasattr(self, 'async_bridge') and self.async_bridge:
                self.batch_operation_requested.emit('instagram_dm_task', task_data)
                self._show_log(f"Instagram粉丝私信任务已启动，涉及{len(selected_emulators)}个模拟器")
                if hasattr(self, 'ui_service') and self.ui_service:
                    self.ui_service.log_emulator(f"用户启动Instagram粉丝私信任务，模拟器数量: {len(selected_emulators)}", component="MainWindowV2")
            else:
                QMessageBox.warning(self, "错误", "系统未就绪，无法启动任务")
                self._show_log("Instagram粉丝私信任务启动失败: 异步桥梁不可用")

        except Exception as e:
            self.logger.error(f"处理Instagram粉丝私信任务请求失败: {e}")
            QMessageBox.critical(self, "错误", f"启动任务失败: {e}")

    def on_instagram_follow_direct_task(self):
        """Instagram直接关注任务"""
        from PyQt6.QtWidgets import QMessageBox

        try:
            # 获取选中的模拟器
            selected_emulators = self.get_selected_emulators()

            if not selected_emulators:
                QMessageBox.warning(self, "警告", "请先选择要执行任务的模拟器")
                return

            # 构建任务数据
            task_data = {
                'emulator_ids': [emulator['id'] for emulator in selected_emulators]
            }

            # 🎯 通过异步桥梁执行任务，确保异步执行和统一验证逻辑
            if hasattr(self, 'async_bridge') and self.async_bridge:
                self.batch_operation_requested.emit('instagram_follow_direct_task', task_data)
                self._show_log(f"Instagram直接关注任务已启动，涉及{len(selected_emulators)}个模拟器")
                if hasattr(self, 'ui_service') and self.ui_service:
                    self.ui_service.log_emulator(f"用户启动Instagram直接关注任务，模拟器数量: {len(selected_emulators)}", component="MainWindowV2")
            else:
                QMessageBox.warning(self, "错误", "系统未就绪，无法启动任务")
                self._show_log("Instagram直接关注任务启动失败: 异步桥梁不可用")

        except Exception as e:
            self.logger.error(f"处理Instagram直接关注任务请求失败: {e}")
            QMessageBox.critical(self, "错误", f"启动任务失败: {e}")

    def on_instagram_follow_fans_task(self):
        """Instagram关注粉丝任务"""
        from PyQt6.QtWidgets import QMessageBox

        try:
            # 获取选中的模拟器
            selected_emulators = self.get_selected_emulators()

            if not selected_emulators:
                QMessageBox.warning(self, "警告", "请先选择要执行任务的模拟器")
                return

            # 构建任务数据
            task_data = {
                'emulator_ids': [emulator['id'] for emulator in selected_emulators]
            }

            # 🎯 通过异步桥梁执行任务，确保异步执行和统一验证逻辑
            if hasattr(self, 'async_bridge') and self.async_bridge:
                self.batch_operation_requested.emit('instagram_follow_fans_task', task_data)
                self._show_log(f"Instagram关注粉丝任务已启动，涉及{len(selected_emulators)}个模拟器")
                if hasattr(self, 'ui_service') and self.ui_service:
                    self.ui_service.log_emulator(f"用户启动Instagram关注粉丝任务，模拟器数量: {len(selected_emulators)}", component="MainWindowV2")
            else:
                QMessageBox.warning(self, "错误", "系统未就绪，无法启动任务")
                self._show_log("Instagram关注粉丝任务启动失败: 异步桥梁不可用")

        except Exception as e:
            self.logger.error(f"处理Instagram关注粉丝任务请求失败: {e}")
            QMessageBox.critical(self, "错误", f"启动任务失败: {e}")

    def on_arrangement_started(self):
        """🎯 窗口排列开始信号处理"""
        try:
            self._show_log("开始排列窗口...")
            # 可以在这里添加UI状态更新，比如禁用排列按钮
            if hasattr(self, 'arrange_windows_btn'):
                self.arrange_windows_btn.setEnabled(False)
                self.arrange_windows_btn.setText("排列中...")
        except Exception as e:
            self.logger.error(f"处理窗口排列开始信号失败: {e}")

    def on_arrangement_completed(self, result: dict):
        """🎯 窗口排列完成信号处理"""
        try:
            message = result.get('message', '窗口排列完成')
            window_count = result.get('window_count', 0)
            success_count = result.get('success_count', 0)

            self._show_log(f"窗口排列完成: {message} (成功: {success_count}/{window_count})")

            # 恢复UI状态
            if hasattr(self, 'arrange_windows_btn'):
                self.arrange_windows_btn.setEnabled(True)
                self.arrange_windows_btn.setText("排列窗口")
        except Exception as e:
            self.logger.error(f"处理窗口排列完成信号失败: {e}")

    def on_arrangement_failed(self, error_message: str):
        """🎯 窗口排列失败信号处理"""
        try:
            self._show_log(f"窗口排列失败: {error_message}", "error")

            # 恢复UI状态
            if hasattr(self, 'arrange_windows_btn'):
                self.arrange_windows_btn.setEnabled(True)
                self.arrange_windows_btn.setText("排列窗口")
        except Exception as e:
            self.logger.error(f"处理窗口排列失败信号失败: {e}")
    
    def __init__(self, parent=None, config_manager=None):
        """
        主窗口初始化
        ========================================
        功能描述: 初始化主窗口，设置核心组件和UI界面
        参数: parent=父窗口, config_manager=统一配置管理器
        ========================================
        """
        super().__init__(parent)

        # 🎯 核心组件初始化 - UI服务层和配置管理
        self.ui_service = get_ui_service_layer(config_manager)
        self.config_manager = self.ui_service.config_manager

        # 简化版：不使用复杂的配置辅助类
        self.config_helper = None
        self.config_save_service = None
        self.window_state_service = None

        # 创建日志记录器
        import logging
        self.logger = logging.getLogger(self.__class__.__name__)

        # UI组件
        self.nav_buttons = []
        self.current_page = 0

        # 初始化模拟器相关变量 - 简化版
        self.emulator_list = []
        self.current_emulator_path = None

        # 初始化界面
        self.init_ui()
        self.setup_button_connections()

        # 在UI完全创建后恢复窗口状态（包括分割器状态和模拟器路径）
        self.restore_window_state()

        # 加载模拟器列表 - 在路径恢复后自动加载
        self._trigger_scan("程序启动")

        # 🎯 设置事件驱动日志更新 - 表格运行日志列实时更新（替代30秒定时器）
        self.setup_event_driven_log_updates()

        # 🎯 延迟初始化Instagram汇总统计
        from PyQt6.QtCore import QTimer
        QTimer.singleShot(2000, self._update_instagram_global_summary)

    def _update_instagram_global_summary(self):
        """更新Instagram全局汇总统计"""
        try:
            # 获取表格模型
            table_model = getattr(self, 'emulator_model', None)
            if not table_model:
                return

            # 更新Instagram私信UI的汇总统计
            if hasattr(self, 'instagram_dm_ui') and self.instagram_dm_ui:
                self.instagram_dm_ui.update_global_summary(table_model)

            # 更新Instagram关注UI的汇总统计
            if hasattr(self, 'instagram_follow_ui') and self.instagram_follow_ui:
                if hasattr(self.instagram_follow_ui, 'update_global_summary'):
                    self.instagram_follow_ui.update_global_summary(table_model)

        except Exception as e:
            self.logger.error(f"更新Instagram全局汇总统计失败: {e}")

    # 🎯 UI界面创建和布局 - 创建主窗口的所有UI组件和分页布局
    # ============================================================================
    # 🎯 2. UI界面创建和布局模块
    # ============================================================================
    # 功能描述: 创建主窗口的所有UI组件，包括菜单栏、工具栏、主界面布局
    # 调用关系: 被__init__方法调用，创建完整的用户界面
    # 注意事项: 使用分页布局，支持模拟器管理、任务管理、设置等页面
    # ============================================================================

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("雷电模拟器中控系统")
        self.setMinimumSize(1200, 800)
        
        # 设置字体
        font = QFont("Microsoft YaHei UI", 9)
        QApplication.setFont(font)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建导航面板
        self.nav_panel = self.create_nav_panel()
        main_layout.addWidget(self.nav_panel)
        
        # 创建内容区域
        self.stacked_content = QStackedWidget()
        self.home_page = self.create_home_page()
        self.task_page = self.create_task_management_page()
        self.app_page = self.create_placeholder_page("应用管理", "应用管理功能正在开发中...")
        self.stats_page = self.create_placeholder_page("数据统计", "数据统计功能正在开发中...")
        self.settings_page = self.create_settings_page()

        # 按导航按钮顺序添加页面
        self.stacked_content.addWidget(self.home_page)      # 索引 0: 首页
        self.stacked_content.addWidget(self.task_page)      # 索引 1: 任务管理
        self.stacked_content.addWidget(self.app_page)       # 索引 2: 应用管理
        self.stacked_content.addWidget(self.stats_page)     # 索引 3: 数据统计
        self.stacked_content.addWidget(self.settings_page)  # 索引 4: 系统设置
        
        main_layout.addWidget(self.stacked_content)
        
        # 设置全局样式
        self.setStyleSheet("""
            QMainWindow { background-color: #f5f5f7; }
            QTableView {
                background-color: white;
                border-radius: 15px;
                border: none;
                alternate-background-color: #fafafa;
                gridline-color: #f0f0f0;
            }
            QTableView::item {
                padding: 5px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableView::item:selected {
                background-color: #f0e6f5;
                color: #8a56ac;
            }
            QTableView::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #ddd;
                border-radius: 4px;
                background-color: white;
                margin: 2px;
            }
            QTableView::indicator:hover {
                border-color: #FF9800;
                background-color: #fff8f0;
            }
            QTableView::indicator:checked {
                background-color: #FF9800;
                border-color: #FF9800;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDNMNC41IDguNUwyIDYiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
            }
            QTableView::indicator:checked:hover {
                background-color: #F57C00;
                border-color: #F57C00;
            }
            QHeaderView::section { 
                background-color: #f8f8f8; 
                padding: 5px; 
                border-top: none;
                border-left: none;
                border-bottom: 1px solid #e0e0e0; 
                border-right: 1px solid #dcdcdc;
                font-weight: bold; 
            }
            QHeaderView::section:last {
                border-right: none;
            }
            QScrollBar:vertical { 
                border: none; 
                background: #f5f5f7; 
                width: 8px; 
                border-radius: 4px; 
            }
            QScrollBar::handle:vertical { 
                background: #c0c0c0; 
                border-radius: 4px; 
                min-height: 20px; 
            }
            QScrollBar::handle:vertical:hover { 
                background: #a0a0a0; 
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical { height: 0px; }
            QSplitter::handle { background-color: #e0e0e0; height: 2px; }
        """)
    
    def create_nav_panel(self):
        """创建导航面板 - 左侧渐变导航栏"""
        nav_frame = GradientFrame(
            start_color=QColor("#8a56ac"), 
            end_color=QColor("#e196e6"), 
            direction=Qt.Orientation.Vertical
        )
        nav_layout = QVBoxLayout(nav_frame)
        nav_layout.setContentsMargins(10, 20, 10, 20)
        nav_layout.setSpacing(10)
        
        # Logo区域
        logo_layout = QHBoxLayout()
        logo_label = QLabel("雷电模拟器中控系统")
        logo_label.setStyleSheet("color: white; font-size: 16px; font-weight: bold;")
        logo_layout.addWidget(logo_label)
        nav_layout.addLayout(logo_layout)
        
        nav_layout.addSpacing(30)
        
        # 导航按钮
        self.btn_home = NavButton("首页", True)
        self.btn_task = NavButton("任务管理")
        self.btn_app = NavButton("应用管理")
        self.btn_stats = NavButton("数据统计")
        self.btn_settings = NavButton("系统设置")

        self.nav_buttons = [
            self.btn_home, self.btn_task, self.btn_app,
            self.btn_stats, self.btn_settings
        ]

        for i, btn in enumerate(self.nav_buttons):
            nav_layout.addWidget(btn)
            btn.clicked.connect(lambda checked=None, idx=i: (lambda: self.on_nav_button_clicked(idx))() if not checked else None)
        
        nav_layout.addStretch()
        
        # 版本信息
        version_label = QLabel("v2.0.0")
        version_label.setStyleSheet("color: rgba(255, 255, 255, 0.7); font-size: 11px;")
        version_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        nav_layout.addWidget(version_label)
        
        nav_frame.setFixedWidth(180)
        return nav_frame
    
    def on_nav_button_clicked(self, index):
        """导航按钮点击处理"""
        # 更新按钮状态
        for i, btn in enumerate(self.nav_buttons):
            btn.set_active(i == index)
        
        # 切换页面
        self.stacked_content.setCurrentIndex(index)
        self.current_page = index
    
    def setup_button_connections(self):
        """设置按钮连接和信号"""
        # 连接路径输入框的文本改变信号，实现自动扫描
        # 注意：此时path_input应该已经在init_ui()中创建了
        if hasattr(self, 'path_input'):
            self.path_input.textChanged.connect(self._on_path_changed)

    def _on_path_changed(self, text):
        """路径改变时的处理 - 自动触发扫描"""
        # 只有当路径不为空且不同于当前路径时才扫描
        if text and text != self.current_emulator_path:
            # 添加短暂延迟，避免频繁触发
            from PyQt6.QtCore import QTimer
            if not hasattr(self, '_scan_timer'):
                self._scan_timer = QTimer()
                self._scan_timer.setSingleShot(True)
                self._scan_timer.timeout.connect(lambda: self._trigger_scan("路径变化"))
            self._scan_timer.stop()
            self._scan_timer.start(500)  # 500ms延迟
    
    def restore_window_state(self):
        """恢复窗口状态 - 简化版"""
        if not self.config_manager:
            # 使用默认大小
            self.resize(1200, 800)
            return

        try:
            # 恢复窗口几何
            geometry = self.config_manager.get("window_state.geometry")
            if geometry:
                self.restoreGeometry(geometry)
            else:
                self.resize(1200, 800)

            # 恢复窗口状态
            state = self.config_manager.get("window_state.state")
            if state:
                self.restoreState(state)

            # 恢复当前页面
            current_page = self.config_manager.get("window_state.current_page", 0)
            if hasattr(self, 'stacked_widget') and current_page < self.stacked_widget.count():
                self.stacked_widget.setCurrentIndex(current_page)
                self.current_page = current_page

            # 恢复模拟器路径
            emulator_path = self.config_manager.get("window_state.emulator_path", "")
            if hasattr(self, 'path_input') and emulator_path:
                self.path_input.setText(emulator_path)

            # 恢复分割器状态
            self.restore_splitter_states()
        except Exception as e:
            # 如果恢复失败，使用默认设置
            self.resize(1200, 800)

    def restore_splitter_states(self):
        """恢复分割器状态 - 简化版"""
        if not self.config_manager:
            self.set_default_splitter_sizes()
            return

        try:
            # 恢复主分割器状态（垂直：表格 vs 底部面板）
            main_splitter_state = self.config_manager.get("window_state.main_splitter_state")
            if main_splitter_state and hasattr(self, 'main_splitter'):
                self.main_splitter.restoreState(main_splitter_state)
                self.logger.debug("主分割器状态已恢复")

            # 恢复底部分割器状态（水平：状态面板 vs 日志面板）
            bottom_splitter_state = self.config_manager.get("window_state.bottom_splitter_state")
            if bottom_splitter_state and hasattr(self, 'bottom_splitter'):
                self.bottom_splitter.restoreState(bottom_splitter_state)
                self.logger.debug("底部分割器状态已恢复")

        except Exception as e:
            # 如果恢复失败，使用默认分割比例
            self.logger.warning(f"恢复分割器状态失败，使用默认设置: {e}")
            self.set_default_splitter_sizes()

    def set_default_splitter_sizes(self):
        """设置默认分割器大小"""
        try:
            # 设置主分割器默认大小（表格:底部面板 = 2:1）
            if hasattr(self, 'main_splitter'):
                self.main_splitter.setSizes([400, 200])

            # 设置底部分割器默认大小（状态面板:日志面板 = 2:1）
            if hasattr(self, 'bottom_splitter'):
                self.bottom_splitter.setSizes([400, 200])

        except Exception as e:
            self.logger.error(f"设置默认分割器大小失败: {e}")

    def closeEvent(self, event):
        """关闭事件处理 - UI层简化版"""
        try:
            # 保存基本窗口状态
            if self.config_manager:
                # 保存窗口几何和状态
                self.config_manager.set("window_state.geometry", self.saveGeometry())
                self.config_manager.set("window_state.state", self.saveState())
                self.config_manager.set("window_state.current_page", self.current_page)

                # 保存模拟器路径
                if hasattr(self, 'path_input'):
                    emulator_path = self.path_input.text()
                    self.config_manager.set("window_state.emulator_path", emulator_path)

                # 保存分割器状态
                if hasattr(self, 'main_splitter'):
                    self.config_manager.set("window_state.main_splitter_state", self.main_splitter.saveState())
                if hasattr(self, 'bottom_splitter'):
                    self.config_manager.set("window_state.bottom_splitter_state", self.bottom_splitter.saveState())

                # 保存配置
                self.config_manager.save()

        except Exception as e:
            print(f"保存窗口状态失败: {e}")

        super().closeEvent(event)

    # ============================================================================
    # 🎯 3. 模拟器管理页面模块
    # ============================================================================
    # 功能描述: 创建模拟器列表显示、操作按钮、状态监控等管理界面
    # 调用关系: 被主界面调用，提供模拟器的增删改查和批量操作功能
    # 注意事项: 集成表格显示、按钮控制、状态统计，支持实时更新
    # ============================================================================

    def create_home_page(self):
        """创建首页 - 模拟器管理界面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 顶部工具栏
        toolbar = self.create_toolbar()
        layout.addWidget(toolbar)

        # 操作按钮区域
        buttons_area = self.create_buttons_area()
        layout.addWidget(buttons_area)

        # 主分割器（垂直：表格 vs 底部面板）
        self.main_splitter = QSplitter(Qt.Orientation.Vertical)
        self.main_splitter.setChildrenCollapsible(False)
        self.main_splitter.setObjectName("main_splitter")

        # 表格容器
        table_container = self.create_table_container()

        # 底部面板（水平分割：状态面板 vs 日志面板）
        bottom_panel = self.create_bottom_panel()

        self.main_splitter.addWidget(table_container)
        self.main_splitter.addWidget(bottom_panel)
        self.main_splitter.setSizes([400, 200])

        layout.addWidget(self.main_splitter)

        return page

    def create_toolbar(self):
        """创建工具栏"""
        from .styled_widgets import RoundedWidget

        toolbar_widget = RoundedWidget()
        toolbar_layout = QHBoxLayout(toolbar_widget)
        toolbar_layout.setContentsMargins(15, 10, 15, 10)

        # 模拟器路径
        path_label = QLabel("模拟器路径:")
        path_label.setStyleSheet("font-weight: bold;")
        toolbar_layout.addWidget(path_label)

        self.path_input = StyledLineEdit()
        self.path_input.setPlaceholderText("请输入雷电模拟器安装路径")
        toolbar_layout.addWidget(self.path_input)

        self.browse_btn = StyledButton("浏览...")
        self.browse_btn.setFixedWidth(80)
        self.browse_btn.clicked.connect(self.browse_emulator_path)
        toolbar_layout.addWidget(self.browse_btn)

        # 搜索框
        self.search_input = StyledLineEdit()
        self.search_input.setPlaceholderText("搜索...")
        self.search_input.setFixedWidth(150)
        toolbar_layout.addWidget(self.search_input)

        # 刷新按钮
        self.refresh_btn = StyledButton("刷新")
        self.refresh_btn.setFixedWidth(80)
        self.refresh_btn.clicked.connect(self.on_refresh_clicked)
        toolbar_layout.addWidget(self.refresh_btn)

        # 设置按钮
        self.settings_btn = StyledButton("设置")
        self.settings_btn.setFixedWidth(80)
        self.settings_btn.clicked.connect(self.on_settings_clicked)
        toolbar_layout.addWidget(self.settings_btn)

        return toolbar_widget

    def create_buttons_area(self):
        """创建按钮区域"""
        from .styled_widgets import RoundedWidget

        buttons_widget = RoundedWidget()
        buttons_layout = QHBoxLayout(buttons_widget)
        buttons_layout.setContentsMargins(15, 10, 15, 10)

        # 基础操作按钮
        self.start_selected_btn = StyledButton("启动选中")
        self.batch_settings_btn = StyledButton("批量设置")

        # 窗口排列按钮
        self.arrange_windows_btn = self.create_task_control_button("排列窗口", "#9c27b0", "#7b1fa2")

        # 任务控制按钮
        self.pause_tasks_btn = self.create_task_control_button("暂停任务", "#ff9800", "#f57c00")
        self.resume_tasks_btn = self.create_task_control_button("恢复任务", "#4caf50", "#388e3c")
        self.close_all_tasks_btn = self.create_task_control_button("关闭所有任务", "#f44336", "#d32f2f")

        # Instagram功能按钮组
        self.instagram_dm_btn = self.create_task_control_button("粉丝私信", "#2196f3", "#1976d2")
        self.instagram_follow_direct_btn = self.create_task_control_button("直接关注", "#4caf50", "#388e3c")
        self.instagram_follow_fans_btn = self.create_task_control_button("关注粉丝", "#ff9800", "#f57c00")

        # 🔗 连接按钮事件处理器
        self.start_selected_btn.clicked.connect(self.on_start_selected)        # 启动选中的模拟器
        self.arrange_windows_btn.clicked.connect(self.on_arrange_windows)      # 排列模拟器窗口
        self.close_all_tasks_btn.clicked.connect(self.on_close_all_tasks)      # 关闭所有任务和模拟器
        self.instagram_dm_btn.clicked.connect(self.on_instagram_dm_task)       # Instagram粉丝私信任务
        self.instagram_follow_direct_btn.clicked.connect(self.on_instagram_follow_direct_task)  # Instagram直接关注任务
        self.instagram_follow_fans_btn.clicked.connect(self.on_instagram_follow_fans_task)      # Instagram关注粉丝任务

        # 📋 添加基础操作按钮到布局
        buttons_layout.addWidget(self.start_selected_btn)      # 启动选中按钮
        buttons_layout.addWidget(self.batch_settings_btn)      # 批量设置按钮
        buttons_layout.addWidget(self.arrange_windows_btn)     # 排列窗口按钮

        # ➖ 添加分隔线（区分基础操作和任务控制）
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.VLine)            # 垂直分隔线
        separator.setFrameShadow(QFrame.Shadow.Sunken)         # 凹陷效果
        separator.setStyleSheet("color: #e0e0e0; margin: 0px 10px;")  # 浅灰色样式
        buttons_layout.addWidget(separator)

        # 🎯 添加任务控制按钮到布局
        buttons_layout.addWidget(self.pause_tasks_btn)         # 暂停任务按钮
        buttons_layout.addWidget(self.resume_tasks_btn)        # 恢复任务按钮
        buttons_layout.addWidget(self.close_all_tasks_btn)     # 关闭所有任务按钮

        # ➖ 添加第二个分隔线（区分Instagram功能）
        separator2 = QFrame()
        separator2.setFrameShape(QFrame.Shape.VLine)           # 垂直分隔线
        separator2.setFrameShadow(QFrame.Shadow.Sunken)        # 凹陷效果
        separator2.setStyleSheet("color: #e0e0e0; margin: 0px 10px;")  # 浅灰色样式
        buttons_layout.addWidget(separator2)

        buttons_layout.addWidget(self.instagram_dm_btn)        # Instagram粉丝私信按钮
        buttons_layout.addWidget(self.instagram_follow_direct_btn)  # Instagram直接关注按钮
        buttons_layout.addWidget(self.instagram_follow_fans_btn)    # Instagram关注粉丝按钮
        buttons_layout.addStretch()                            # 添加弹性空间，按钮左对齐

        return buttons_widget

    def create_task_control_button(self, text, primary_color, hover_color):
        """创建任务控制按钮"""
        button = StyledButton(text)
        button.setStyleSheet(f"""
            QPushButton {{
                background-color: {primary_color};
                color: white;
                border-radius: 18px;
                padding: 8px 16px;
                font-size: 13px;
                font-weight: bold;
                border: none;
            }}
            QPushButton:hover {{
                background-color: {hover_color};
            }}
            QPushButton:pressed {{
                background-color: {hover_color};
            }}
            QPushButton:disabled {{
                background-color: #cccccc;
                color: #999999;
            }}
        """)
        button.setCursor(Qt.CursorShape.PointingHandCursor)
        button.setMinimumHeight(36)
        return button

    def create_table_container(self):
        """创建表格容器"""
        from .styled_widgets import RoundedWidget

        table_container = RoundedWidget()
        table_layout = QVBoxLayout(table_container)
        table_layout.setContentsMargins(0, 0, 0, 0)

        # 创建优化的表格视图
        self.table_view = OptimizedTableView()
        self.init_table_view()
        table_layout.addWidget(self.table_view)

        return table_container

    def init_table_view(self):
        """初始化表格视图"""
        # 🎯 创建并设置模型 - 统一命名为emulator_model，传入UI服务层
        self.emulator_model = EmulatorTableModel(parent=self, ui_service=self.ui_service)
        self.table_model = self.emulator_model  # 兼容性别名
        self.table_view.setModel(self.emulator_model)

        # 设置表格属性
        self.table_view.setAlternatingRowColors(True)
        self.table_view.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.table_view.setSelectionMode(QTableView.SelectionMode.MultiSelection)

        # 设置表头
        header = self.table_view.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)
        header.setStretchLastSection(True)

        # 设置垂直表头
        v_header = self.table_view.verticalHeader()
        v_header.setVisible(False)

        # 设置右键菜单
        self.table_view.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.table_view.customContextMenuRequested.connect(self.show_context_menu)

        # 不在初始化时自动加载数据，等待load_emulator_list调用

    def create_bottom_panel(self):
        """创建底部面板"""
        from .styled_widgets import RoundedWidget

        bottom_splitter = QSplitter(Qt.Orientation.Horizontal)
        bottom_splitter.setChildrenCollapsible(False)
        bottom_splitter.setObjectName("bottom_splitter")

        # 状态面板
        status_container = RoundedWidget()
        grid_layout = QGridLayout(status_container)
        self.create_status_grid(grid_layout)
        bottom_splitter.addWidget(status_container)

        # 日志面板
        log_container = RoundedWidget()
        log_layout = QVBoxLayout(log_container)
        log_layout.setContentsMargins(15, 10, 15, 15)

        log_title = QLabel("系统日志")
        log_title.setStyleSheet("font-weight: bold; font-size: 13px; margin-bottom: 5px;")

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 5px;
            }
        """)

        # 添加示例日志内容
        sample_logs = [
            "[23:41:03] INFO: 正在加载雷电模拟器配置...",
            "[23:41:03] INFO: 正在检测雷电模拟器运行状态...",
            "[23:41:03] INFO: 正在检测雷电模拟器运行状态...",
        ]
        self.log_text.setPlainText("\n".join(sample_logs))

        log_layout.addWidget(log_title)
        log_layout.addWidget(self.log_text)
        bottom_splitter.addWidget(log_container)

        # 设置默认分割比例 (状态面板:日志面板 = 2:1)
        bottom_splitter.setSizes([400, 200])

        # 保存分割器引用
        self.bottom_splitter = bottom_splitter

        return bottom_splitter

    def create_status_grid(self, grid_layout):
        """创建状态网格"""
        grid_layout.setContentsMargins(25, 20, 25, 20)
        grid_layout.setHorizontalSpacing(20)
        grid_layout.setVerticalSpacing(15)

        # 模拟器状态列
        self.create_emulator_status_column(grid_layout)

        # 分隔线1
        separator1 = QFrame()
        separator1.setFrameShape(QFrame.Shape.VLine)
        separator1.setFrameShadow(QFrame.Shadow.Sunken)
        separator1.setStyleSheet("color: #e5e5e5;")
        grid_layout.addWidget(separator1, 0, 1, 2, 1)

        # 任务进度列
        self.create_task_progress_column(grid_layout)

        # 分隔线2
        separator2 = QFrame()
        separator2.setFrameShape(QFrame.Shape.VLine)
        separator2.setFrameShadow(QFrame.Shadow.Sunken)
        separator2.setStyleSheet("color: #e5e5e5;")
        grid_layout.addWidget(separator2, 0, 3, 2, 1)

        # 系统资源列
        self.create_system_resource_column(grid_layout)

        # 设置列拉伸
        grid_layout.setColumnStretch(0, 3)
        grid_layout.setColumnStretch(2, 2)
        grid_layout.setColumnStretch(4, 3)

    def create_emulator_status_column(self, grid):
        """创建模拟器状态列"""
        title = QLabel("模拟器状态")
        title.setStyleSheet("font-size: 14px; font-weight: bold; color: #333;")
        grid.addWidget(title, 0, 0)

        content_widget = QWidget()
        content_layout = QHBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(15)

        # 环形图
        self.emulator_donut = DonutWidget()
        self.emulator_donut.setFixedSize(70, 70)
        self.emulator_donut.set_values(0, 0)
        content_layout.addWidget(self.emulator_donut)

        # 文本信息
        text_layout = QVBoxLayout()
        text_layout.setSpacing(5)
        text_layout.addStretch()

        self.running_label = QLabel("0 运行中")
        self.total_label = QLabel("1228 总数")
        self.running_label.setStyleSheet("font-size: 15px; font-weight: bold; color:rgb(33, 167, 55);")
        self.total_label.setStyleSheet("font-size: 15px; color:rgb(15, 15, 15);")

        text_layout.addWidget(self.running_label)
        text_layout.addWidget(self.total_label)
        text_layout.addStretch()

        content_layout.addLayout(text_layout)
        grid.addWidget(content_widget, 1, 0)

    def create_task_progress_column(self, grid):
        """创建任务进度列"""
        title = QLabel("任务进度")
        title.setStyleSheet("font-size: 14px; font-weight: bold; color: #333;")
        grid.addWidget(title, 0, 2)

        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(8)

        self.task_count_label = QLabel("已完成: 0 / 0")
        self.task_count_label.setStyleSheet("font-size: 15px; font-weight: bold; color:rgb(33, 167, 55);")
        content_layout.addWidget(self.task_count_label)

        self.task_progress_bar = QProgressBar()
        self.task_progress_bar.setValue(0)
        self.task_progress_bar.setTextVisible(False)
        self.task_progress_bar.setStyleSheet("""
            QProgressBar {
                background-color: #f0f0f0;
                border-radius: 5px;
                height: 10px;
            }
            QProgressBar::chunk {
                background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #8a56ac, stop:1 #c59de0);
                border-radius: 5px;
            }
        """)
        content_layout.addWidget(self.task_progress_bar)
        content_layout.addStretch()

        grid.addWidget(content_widget, 1, 2, Qt.AlignmentFlag.AlignTop)

    def create_system_resource_column(self, grid):
        """创建系统资源列"""
        title = QLabel("系统资源")
        title.setStyleSheet("font-size: 14px; font-weight: bold; color: #333;")
        grid.addWidget(title, 0, 4)

        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(8)

        # CPU进度条
        self.cpu_progress_bar = QProgressBar()
        content_layout.addWidget(self.create_resource_item("CPU", self.cpu_progress_bar))

        # 内存进度条
        self.mem_progress_bar = QProgressBar()
        content_layout.addWidget(self.create_resource_item("内存", self.mem_progress_bar))

        grid.addWidget(content_widget, 1, 4)

    def create_resource_item(self, name, progress_bar):
        """创建资源项"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)

        label = QLabel(name)
        label.setFixedWidth(30)
        label.setStyleSheet("font-size: 12px;")

        progress_bar.setValue(0)
        progress_bar.setFormat("%p%")
        progress_bar.setTextVisible(True)
        progress_bar.setStyleSheet("""
            QProgressBar {
                background-color: #f0f0f0;
                border: none;
                border-radius: 8px;
                height: 16px;
                text-align: right;
                padding-right: 5px;
                color: #555;
            }
            QProgressBar::chunk {
                background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #a0e9ff, stop:1 #a1c4fd);
                border-radius: 8px;
            }
        """)

        layout.addWidget(label)
        layout.addWidget(progress_bar)
        return widget

    def create_settings_page(self):
        """创建设置页面 - 传入共享的配置管理器"""
        from .settings_ui import SettingsUI
        return SettingsUI(parent=self, config_manager=self.config_manager)

    def create_placeholder_page(self, title, description):
        """创建占位页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(50, 50, 50, 50)

        # 居中容器
        center_widget = QWidget()
        center_layout = QVBoxLayout(center_widget)
        center_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        center_layout.setSpacing(20)

        # 图标
        icon_label = QLabel("🚧")
        icon_label.setFont(QFont("Microsoft YaHei", 48))
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        center_layout.addWidget(icon_label)

        # 标题
        title_label = QLabel(title)
        title_label.setFont(QFont("Microsoft YaHei", 24, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #333333;")
        center_layout.addWidget(title_label)

        # 描述
        desc_label = QLabel(description)
        desc_label.setFont(QFont("Microsoft YaHei", 14))
        desc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        desc_label.setStyleSheet("color: #666666;")
        center_layout.addWidget(desc_label)

        layout.addWidget(center_widget)

        return page

    # ============================================================================
    # 🎯 4. 任务管理页面模块
    # ============================================================================
    # 功能描述: 创建各种自动化任务的配置和管理界面，支持标签页扩展
    # 调用关系: 被主界面调用，集成Instagram私信等任务配置UI
    # 注意事项: 使用标签页设计，便于后续扩展其他任务类型
    # ============================================================================

    def create_task_management_page(self):
        """创建任务管理页面 - Instagram私信等任务配置和管理界面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 标题
        title_label = QLabel("任务管理")
        title_label.setStyleSheet("font-size: 22px; font-weight: bold; color: #333; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # 创建标签页 - 复用系统设置的样式
        self.task_tab_widget = QTabWidget()
        self.task_tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                background-color: white;
                margin-top: 5px;
            }
            QTabBar::tab {
                background-color: #f5f5f5;
                color: #666;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: white;
                color: #8a56ac;
                border-bottom: 2px solid #8a56ac;
            }
            QTabBar::tab:hover {
                background-color: #f0f0f0;
                color: #333;
            }
        """)

        # 创建任务页面
        self.basic_config_page = self.create_basic_config_page()
        self.instagram_dm_task_page = self.create_instagram_dm_task_page()
        self.instagram_follow_task_page = self.create_instagram_follow_task_page()
        self.other_task_page_2 = self.create_placeholder_task_page("其他任务2", "其他任务2功能正在开发中...")

        self.task_tab_widget.addTab(self.basic_config_page, "基础配置")
        self.task_tab_widget.addTab(self.instagram_dm_task_page, "粉丝私信")
        self.task_tab_widget.addTab(self.instagram_follow_task_page, "关注任务")
        self.task_tab_widget.addTab(self.other_task_page_2, "其他任务2")

        layout.addWidget(self.task_tab_widget)
        return page

    def create_basic_config_page(self):
        """创建基础配置页面 - 集成热加载功能"""
        try:
            from .basic_config_ui import BasicConfigUI

            # 创建基础配置UI，传入配置管理器确保一致性
            basic_config_ui = BasicConfigUI(parent=self)

            # 连接配置变更信号（可选，用于状态反馈）
            basic_config_ui.config_changed.connect(self.on_basic_config_changed)

            return basic_config_ui

        except Exception as e:
            self.logger.error(f"创建基础配置页面失败: {e}")
            # 创建错误页面
            error_page = QWidget()
            error_layout = QVBoxLayout(error_page)
            error_label = QLabel(f"基础配置页面加载失败: {e}")
            error_label.setStyleSheet("color: red; font-size: 14px;")
            error_layout.addWidget(error_label)
            return error_page

    def on_basic_config_changed(self, key: str, value):
        """基础配置变更回调"""
        try:
            self.logger.info(f"基础配置已变更: {key} = {value}")
            # 这里可以添加配置变更的UI反馈
        except Exception as e:
            self.logger.error(f"处理基础配置变更事件失败: {e}")

    def create_instagram_dm_task_page(self):
        """创建Instagram私信任务页面 - 集成Instagram私信配置UI"""
        try:
            from .instagram_dm_ui import InstagramDMUI
            instagram_dm_page = InstagramDMUI(config_manager=self.config_manager)

            # 连接Instagram私信页面的信号
            instagram_dm_page.task_start_requested.connect(self._on_instagram_dm_start)
            instagram_dm_page.task_stop_requested.connect(self._on_instagram_dm_stop)

            return instagram_dm_page

        except Exception as e:
            print(f"创建Instagram私信页面失败: {e}")
            # 创建错误页面
            error_page = QWidget()
            error_layout = QVBoxLayout(error_page)
            error_label = QLabel(f"Instagram私信页面加载失败: {e}")
            error_label.setStyleSheet("color: red; font-size: 14px;")
            error_layout.addWidget(error_label)
            return error_page

    def create_instagram_follow_task_page(self):
        """创建Instagram关注任务页面 - 集成Instagram关注配置UI"""
        try:
            from .instagram_follow_ui import InstagramFollowUI
            instagram_follow_page = InstagramFollowUI(config_manager=self.config_manager)

            # 连接Instagram关注页面的信号
            instagram_follow_page.task_start_requested.connect(self._on_instagram_follow_start)
            instagram_follow_page.task_stop_requested.connect(self._on_instagram_follow_stop)

            return instagram_follow_page

        except Exception as e:
            print(f"创建Instagram关注页面失败: {e}")
            # 创建错误页面
            error_page = QWidget()
            error_layout = QVBoxLayout(error_page)
            error_label = QLabel(f"Instagram关注页面加载失败: {e}")
            error_label.setStyleSheet("color: red; font-size: 14px;")
            error_layout.addWidget(error_label)
            return error_page

    def create_placeholder_task_page(self, title, message):
        """创建占位任务页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(40, 40, 40, 40)
        layout.setSpacing(20)

        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #333;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 消息
        message_label = QLabel(message)
        message_label.setStyleSheet("font-size: 14px; color: #666;")
        message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        layout.addStretch()
        layout.addWidget(title_label)
        layout.addWidget(message_label)
        layout.addStretch()

        return page

    # ============================================================================
    # 🎯 5. Instagram私信任务处理模块
    # ============================================================================
    # 功能描述: 处理Instagram私信任务的启动、停止、状态更新等操作
    # 调用关系: 被Instagram私信UI调用，通过异步桥梁执行任务
    # 注意事项: 使用异步处理确保不阻塞UI，集成统一配置和状态管理
    # ============================================================================

    def _on_instagram_dm_start(self):
        """Instagram私信任务开始请求处理 - 使用异步桥梁"""
        try:
            self.logger.info("收到Instagram私信任务开始请求")

            # 获取选中的模拟器
            selected_emulators = self.get_selected_emulators()

            if not selected_emulators:
                self._show_log("Instagram私信任务启动失败: 未选择模拟器")
                return

            # 准备任务数据
            task_data = {
                'emulators': selected_emulators,
                'task_type': 'instagram_dm',
                'timestamp': self._get_current_timestamp()
            }

            # 🎯 通过异步桥梁执行任务，完全异步，不阻塞UI
            if hasattr(self, 'async_bridge') and self.async_bridge:
                self.async_bridge.execute_operation('instagram_dm_task', task_data)
                self.logger.info("Instagram私信任务已提交到异步桥梁")
                self._show_log(f"Instagram私信任务已启动，涉及{len(selected_emulators)}个模拟器")
            else:
                self.logger.warning("异步桥梁不可用，无法启动Instagram私信任务")
                self._show_log("Instagram私信任务启动失败: 异步桥梁不可用")

        except Exception as e:
            self.logger.error(f"处理Instagram私信任务开始请求失败: {e}")
            self._show_log(f"Instagram私信任务启动失败: {e}")

    def _on_instagram_dm_stop(self):
        """Instagram私信任务停止请求处理"""
        try:
            self.logger.info("收到Instagram私信任务停止请求")
            # TODO: 实现停止Instagram私信任务的逻辑
            self._show_log("Instagram私信任务停止功能正在开发中...")
        except Exception as e:
            self.logger.error(f"处理Instagram私信任务停止请求失败: {e}")

    def _on_instagram_follow_start(self):
        """Instagram关注任务开始请求处理 - 使用异步桥梁"""
        try:
            self.logger.info("收到Instagram关注任务开始请求")

            # 获取选中的模拟器
            selected_emulators = self.get_selected_emulators()

            if not selected_emulators:
                self._show_log("Instagram关注任务启动失败: 未选择模拟器")
                return

            # 构建任务数据
            task_data = {
                'emulator_ids': [emulator['id'] for emulator in selected_emulators],
                'timestamp': self._get_current_timestamp()
            }

            # 🎯 通过异步桥梁执行任务，完全异步，不阻塞UI
            if hasattr(self, 'async_bridge') and self.async_bridge:
                self.async_bridge.execute_operation('instagram_follow_direct_task', task_data)
                self.logger.info("Instagram关注任务已提交到异步桥梁")
                self._show_log(f"Instagram关注任务已启动，涉及{len(selected_emulators)}个模拟器")
            else:
                self.logger.warning("异步桥梁不可用，无法启动Instagram关注任务")
                self._show_log("Instagram关注任务启动失败: 异步桥梁不可用")

        except Exception as e:
            self.logger.error(f"处理Instagram关注任务开始请求失败: {e}")
            self._show_log(f"Instagram关注任务启动失败: {e}")

    def _on_instagram_follow_stop(self):
        """Instagram关注任务停止请求处理"""
        try:
            self.logger.info("收到Instagram关注任务停止请求")
            # TODO: 实现停止Instagram关注任务的逻辑
            self._show_log("Instagram关注任务停止功能正在开发中...")
        except Exception as e:
            self.logger.error(f"处理Instagram关注任务停止请求失败: {e}")

    def _get_current_timestamp(self):
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def _trigger_scan(self, source="未知来源"):
        """触发扫描 - 修复版，快速扫描后应用状态"""
        # 🎯 获取模拟器路径
        emulator_path = ""
        if hasattr(self, 'path_input'):
            emulator_path = self.path_input.text()

        # 🎯 路径为空就静默返回
        if emulator_path == "":
            return

        # 🎯 关键修复：更新当前路径状态，防止重复扫描
        self.current_emulator_path = emulator_path

        # 🎯 显示扫描开始日志
        self._show_log(f"开始扫描模拟器 (来源: {source})")

        # 🎯 关键修复：检查异步桥梁连接状态，延迟扫描直到连接完成
        if hasattr(self, 'async_bridge') and self.async_bridge:
            self.batch_operation_requested.emit("scan_emulators", emulator_path)
        else:
            # 🎯 延迟扫描直到异步桥梁连接完成
            if not hasattr(self, '_pending_scan_requests'):
                self._pending_scan_requests = []
            self._pending_scan_requests.append((emulator_path, source))

    def _show_log(self, message: str):
        """显示日志信息 - 参考代码的日志显示方式"""
        # 只在日志区域显示，不弹框
        if hasattr(self, 'log_text'):
            from datetime import datetime
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.log_text.append(f"[{timestamp}] {message}")

        # 记录到日志文件
        self.logger.info(message)

    def process_pending_scan_requests(self):
        """🎯 处理延迟的扫描请求 - 当异步桥梁连接完成时调用"""
        if hasattr(self, '_pending_scan_requests') and self._pending_scan_requests:
            for emulator_path, _ in self._pending_scan_requests:
                self.batch_operation_requested.emit("scan_emulators", emulator_path)

            # 清空延迟请求列表
            self._pending_scan_requests.clear()

    def _populate_table_data(self):
        """🎯 填充表格数据 - 删除重复的转换方法，直接内联处理"""
        try:
            # 🎯 清空现有数据
            if hasattr(self, 'table_model'):
                self.table_model.clear_data()

            # 🎯 使用统一的格式化方法
            if self.emulator_list:
                from core.status_converter import format_emulator_list_for_table
                table_data = format_emulator_list_for_table(self.emulator_list)

                if hasattr(self, 'table_model'):
                    self.table_model.set_data(table_data)

            # 更新统计信息
            self._update_statistics()

        except Exception as e:
            self._show_log(f"填充表格数据失败: {e}")

    def _update_statistics(self):
        """更新统计信息 - 使用统一的统计计算"""
        try:
            # 🎯 使用统一的统计计算方法
            from core.status_converter import get_emulator_statistics
            running_count, total_count = get_emulator_statistics(self.emulator_list)

            # 🎯 更新状态显示标签
            if hasattr(self, 'running_label'):
                self.running_label.setText(f"{running_count} 运行中")

            if hasattr(self, 'total_label'):
                self.total_label.setText(f"{total_count} 总数")

            # 🎯 更新甜甜圈图显示
            if hasattr(self, 'emulator_donut'):
                self.emulator_donut.set_values(running_count, total_count)

        except Exception as e:
            # 静默处理统计更新错误
            pass

    # 🎯 事件处理方法
    def browse_emulator_path(self):
        """浏览模拟器路径 - 恢复硬盘状态重置版"""
        from PyQt6.QtWidgets import QFileDialog

        # 🎯 打开文件夹选择对话框
        file_dialog = QFileDialog()
        directory = file_dialog.getExistingDirectory(
            self,
            "选择雷电模拟器安装目录",
            self.path_input.text() or "C:\\"
        )

        if directory:
            # 🎯 设置路径到输入框
            self.path_input.setText(directory)

            # 🎯 保存配置到统一配置系统
            if self.config_manager:
                self.config_manager.set("window_state.emulator_path", directory)
                self.config_manager.save()

            # 🎯 重置路径状态并触发扫描
            self.current_emulator_path = None
            self._trigger_scan("路径浏览")

    # ============================================================================
    # 🎯 7. 事件驱动日志更新模块
    # ============================================================================
    # 功能描述: 处理表格运行日志列的实时更新，替代定时器方案
    # 调用关系: 被日志管理器通过信号调用，更新单个模拟器的运行日志
    # 注意事项: 区分表格运行日志列和系统日志面板，使用事件驱动更新
    # ============================================================================

    def setup_event_driven_log_updates(self):
        """🎯 设置事件驱动日志更新 - 表格运行日志列实时更新（替代30秒定时器）"""
        try:
            # 获取全局日志管理器
            from core.logger_manager import get_logger_manager
            logger_manager = get_logger_manager()

            # 连接模拟器日志更新信号 - 专用于表格运行日志列
            logger_manager.emulator_log_updated.connect(self.on_emulator_log_updated)

            # 🎯 连接Instagram任务状态更新信号 - 专用于任务状态列
            logger_manager.instagram_task_status_updated.connect(self.on_instagram_task_status_updated)

            self.logger.info("事件驱动日志更新已启动 - 表格运行日志列实时更新（替代定时器）")
            self.logger.info("Instagram任务状态更新信号已连接")

        except Exception as e:
            self.logger.error(f"设置事件驱动日志更新失败: {e}")

    def on_emulator_log_updated(self, emulator_id: int, log_message: str):
        """🎯 处理模拟器日志更新事件 - 通过业务层更新运行日志列"""
        try:
            if not hasattr(self, 'emulator_model') or not self.emulator_model:
                return

            # 🎯 从日志消息中提取简化的状态信息
            simplified_log = self._simplify_log_message(log_message)

            # 🎯 通过业务层更新运行日志，而不是直接操作表格数据
            from core.status_converter import get_ui_update_manager
            ui_manager = get_ui_update_manager(self.emulator_model)

            # 查找模拟器当前状态
            row = ui_manager._find_emulator_row(emulator_id)
            if row != -1:
                current_status = self.emulator_model.emulators[row][3]  # 获取当前模拟器状态

                # 🎯 通过详细更新接口更新运行日志
                detailed_data = {'running_log': simplified_log}
                success = ui_manager.update_table_row_detailed(emulator_id, current_status, detailed_data)

                if success:
                    self.logger.debug(f"模拟器{emulator_id}运行日志已通过业务层更新: {simplified_log}")
                else:
                    # 如果业务层更新失败，作为备用方案直接更新（保持兼容性）
                    old_log = self.emulator_model.emulators[row][9]
                    if old_log != simplified_log:
                        self.emulator_model.emulators[row][9] = simplified_log
                        index = self.emulator_model.index(row, 9)
                        self.emulator_model.dataChanged.emit(index, index)
                        self.logger.debug(f"模拟器{emulator_id}运行日志已直接更新（备用方案）: {simplified_log}")
            else:
                self.logger.warning(f"未找到模拟器{emulator_id}，无法更新运行日志")

        except Exception as e:
            self.logger.error(f"处理运行日志更新事件失败: {e}")

    def on_instagram_task_status_updated(self, emulator_id: int, task_status: str):
        """🎯 处理Instagram任务状态更新事件 - 专用于任务状态列更新"""
        try:
            if not hasattr(self, 'emulator_model') or not self.emulator_model:
                return

            # 🎯 使用专门的Instagram任务状态更新方法
            from core.status_converter import get_ui_update_manager
            ui_manager = get_ui_update_manager(self.emulator_model)

            success = ui_manager.update_instagram_task_status(emulator_id, task_status)

            if success:
                self.logger.info(f"模拟器{emulator_id}的Instagram任务状态已更新: {task_status}")
            else:
                self.logger.warning(f"模拟器{emulator_id}的Instagram任务状态更新失败")

        except Exception as e:
            self.logger.error(f"处理Instagram任务状态更新事件失败: {e}")

    def _simplify_log_message(self, log_message: str) -> str:
        """🎯 简化日志消息 - 提取关键状态用于表格运行日志列显示"""
        try:
            # 根据日志内容判断状态
            if "启动成功" in log_message or "运行中" in log_message:
                return "模拟器运行正常"
            elif "异常" in log_message or "失败" in log_message or "错误" in log_message:
                return "检测到异常活动"
            elif "启动中" in log_message or "启动" in log_message:
                return "模拟器启动中"
            elif "停止" in log_message or "关闭" in log_message:
                return "模拟器已停止"
            elif "心跳异常" in log_message:
                return "心跳检测异常"
            elif "恢复正常" in log_message:
                return "模拟器已恢复"
            else:
                # 提取时间戳和关键信息
                import re
                # 尝试提取状态变化信息
                status_match = re.search(r'模拟器\d+:\s*(.+)', log_message)
                if status_match:
                    return status_match.group(1).strip()

                # 如果没有特殊状态，返回简化的消息
                return log_message[:50] + "..." if len(log_message) > 50 else log_message

        except Exception:
            return "日志解析失败"

    def on_refresh_clicked(self):
        """刷新按钮 - 简化版，每次都重新扫描"""
        # 重置路径状态
        self.current_emulator_path = None
        self._show_log("开始重新扫描...")
        self._trigger_scan("手动刷新")

    def on_settings_clicked(self):
        """设置按钮点击事件"""
        self.settings_requested.emit()

    def show_context_menu(self, position):
        """显示右键菜单 - 直接包含复选框操作功能"""
        # 检查是否点击在有效行上
        index = self.table_view.indexAt(position)
        current_row = index.row() if index.isValid() else -1

        menu = QMenu(self)
        menu.setStyleSheet(self._get_modern_menu_style())

        # 全部勾选
        select_all_action = QAction("全部勾选", self)
        select_all_action.triggered.connect(self.select_all_checkboxes)
        menu.addAction(select_all_action)

        # 取消勾选
        unselect_all_action = QAction("取消勾选", self)
        unselect_all_action.triggered.connect(self.unselect_all_checkboxes)
        menu.addAction(unselect_all_action)

        # 从本行开始勾选
        if current_row >= 0:
            select_from_action = QAction("从本行开始勾选", self)
            select_from_action.triggered.connect(lambda: self.select_from_current_row(current_row))
            menu.addAction(select_from_action)

        # 跳转到指定序号
        jump_to_action = QAction("跳转到指定序号", self)
        jump_to_action.triggered.connect(self.jump_to_row)
        menu.addAction(jump_to_action)

        # 显示菜单
        menu.exec(self.table_view.mapToGlobal(position))

    def _get_modern_menu_style(self):
        """获取现代化菜单样式 - 简化版本"""
        return """
            QMenu {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 4px 0px;
                font-size: 13px;
                color: #333;
                font-weight: 500;
            }
            QMenu::item {
                padding: 8px 16px;
                margin: 1px 4px;
                border-radius: 4px;
                background-color: transparent;
            }
            QMenu::item:selected {
                background-color: #FF9800;
                color: white;
            }
            QMenu::item:pressed {
                background-color: #F57C00;
            }
            QMenu::separator {
                height: 1px;
                background-color: #f0f0f0;
                margin: 4px 8px;
            }
        """

    def _set_checkbox_state_batch(self, state, start_row=0, count=None):
        """统一的复选框状态设置方法 - 避免重复代码"""
        try:
            total_rows = self.table_model.rowCount()
            if count is None:
                count = total_rows - start_row

            end_row = min(start_row + count, total_rows)
            for row in range(start_row, end_row):
                index = self.table_model.index(row, 0)
                self.table_model.setData(index, state, Qt.ItemDataRole.CheckStateRole)

            return end_row - start_row  # 返回实际操作的行数
        except Exception as e:
            self._show_log(f"设置复选框状态失败: {e}")
            return 0

    def select_all_checkboxes(self):
        """全部勾选"""
        count = self._set_checkbox_state_batch(Qt.CheckState.Checked)
        self._show_log(f"已全部勾选 {count} 个模拟器")

    def unselect_all_checkboxes(self):
        """取消勾选"""
        count = self._set_checkbox_state_batch(Qt.CheckState.Unchecked)
        self._show_log(f"已取消 {count} 个勾选")

    def select_from_current_row(self, start_row):
        """从本行开始勾选 - 带输入框显示剩余数量"""
        try:
            total_rows = self.table_model.rowCount()
            remaining_count = total_rows - start_row

            # 创建现代化自定义输入对话框
            count, ok = ModernInputDialog.get_int(
                self,
                "从本行开始勾选",
                f"从第 {start_row + 1} 行开始，剩余 {remaining_count} 个模拟器\n请输入要勾选的数量:",
                remaining_count,
                1,
                remaining_count
            )

            if ok:
                # 先取消所有勾选，再从指定行开始勾选
                self.unselect_all_checkboxes()
                actual_count = self._set_checkbox_state_batch(Qt.CheckState.Checked, start_row, count)
                self._show_log(f"已从第 {start_row + 1} 行开始勾选 {actual_count} 个模拟器")
        except Exception as e:
            self._show_log(f"从本行开始勾选失败: {e}")

    def jump_to_row(self):
        """跳转到指定序号"""
        try:
            total_rows = self.table_model.rowCount()
            if total_rows == 0:
                QMessageBox.information(self, "提示", "当前没有模拟器数据")
                return

            row_number, ok = ModernInputDialog.get_int(
                self,
                "跳转到指定序号",
                f"请输入要跳转的行号 (1-{total_rows}):",
                1,
                1,
                total_rows
            )

            if ok:
                # 转换为0基索引
                target_row = row_number - 1

                # 选中并滚动到指定行
                index = self.table_model.index(target_row, 0)
                self.table_view.setCurrentIndex(index)
                self.table_view.scrollTo(index, QTableView.ScrollHint.PositionAtCenter)

                self._show_log(f"已跳转到第 {row_number} 行")
        except Exception as e:
            self._show_log(f"跳转失败: {e}")



    def _get_checked_rows(self):
        """统一的复选框状态检查方法 - 避免重复逻辑"""
        checked_rows = []
        try:
            for row in range(self.table_model.rowCount()):
                checkbox_index = self.table_model.index(row, 0)
                is_checked = self.table_model.data(checkbox_index, Qt.ItemDataRole.CheckStateRole) == Qt.CheckState.Checked
                if is_checked:
                    checked_rows.append(row)
        except Exception as e:
            self._show_log(f"检查复选框状态失败: {e}")
        return checked_rows

    def get_selected_emulator_ids(self):
        """获取选中的模拟器ID列表"""
        selected_ids = []
        try:
            checked_rows = self._get_checked_rows()
            for row in checked_rows:
                # 获取模拟器ID（假设在第2列，索引为1）
                id_index = self.table_model.index(row, 1)
                emulator_id = self.table_model.data(id_index, Qt.ItemDataRole.DisplayRole)
                if emulator_id is not None:
                    selected_ids.append(emulator_id)
        except Exception as e:
            self._show_log(f"获取选中模拟器失败: {e}")
        return selected_ids

    def get_selected_emulator_count(self):
        """获取选中的模拟器数量"""
        return len(self._get_checked_rows())

    def get_selected_emulators(self):
        """
        获取选中的模拟器信息列表
        ========================================
        功能描述: 获取选中模拟器的完整信息，包括ID、名称等
        返回格式: [{'id': 1, 'name': '模拟器1', ...}, ...]
        调用关系: 被Instagram任务等功能调用
        ========================================
        """
        selected_emulators = []
        try:
            checked_rows = self._get_checked_rows()
            for row in checked_rows:
                # 获取模拟器ID（第2列，索引为1）
                id_index = self.table_model.index(row, 1)
                emulator_id = self.table_model.data(id_index, Qt.ItemDataRole.DisplayRole)

                # 获取模拟器名称（第3列，索引为2）
                name_index = self.table_model.index(row, 2)
                emulator_name = self.table_model.data(name_index, Qt.ItemDataRole.DisplayRole)

                if emulator_id is not None:
                    emulator_info = {
                        'id': emulator_id,
                        'name': emulator_name or f"模拟器{emulator_id}",
                        'row': row
                    }
                    selected_emulators.append(emulator_info)

        except Exception as e:
            self.logger.error(f"获取选中模拟器信息失败: {e}")
            self._show_log(f"获取选中模拟器信息失败: {e}")

        return selected_emulators


# ============================================================================
# 🎯 8. 优化表格视图模块
# ============================================================================
# 功能描述: 修复复选框点击问题的优化表格视图
# 调用关系: 替换标准QTableView，提供优化的复选框交互
# 注意事项: 直接处理复选框点击，避免延迟问题
# 架构合规: UI层组件，只处理界面交互
# ============================================================================

class OptimizedTableView(QTableView):
    """优化的表格视图 - 修复复选框点击问题"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_optimizations()

    def setup_optimizations(self):
        """设置性能优化"""
        # 关键优化：禁用排序提高性能
        self.setSortingEnabled(False)

        # 优化滚动
        self.setVerticalScrollMode(QAbstractItemView.ScrollMode.ScrollPerPixel)
        self.setHorizontalScrollMode(QAbstractItemView.ScrollMode.ScrollPerPixel)

        # 禁用鼠标跟踪提升性能
        self.setMouseTracking(False)

    def mousePressEvent(self, event):
        """重写鼠标按下事件 - 优化复选框点击"""
        if event.button() == Qt.MouseButton.LeftButton:
            index = self.indexAt(event.pos())

            if index.isValid() and index.column() == 0:
                # 直接处理复选框切换
                model = self.model()
                if model:
                    current_state = model.data(index, Qt.ItemDataRole.CheckStateRole)
                    new_state = Qt.CheckState.Unchecked if current_state == Qt.CheckState.Checked else Qt.CheckState.Checked

                    # 直接调用setData
                    success = model.setData(index, new_state, Qt.ItemDataRole.CheckStateRole)

                    if success:
                        return  # 成功处理，不调用父类方法

        # 调用父类方法处理其他情况
        super().mousePressEvent(event)

    def mouseDoubleClickEvent(self, event):
        """重写双击事件"""
        # 对于复选框列，双击也应该处理状态切换
        if event.button() == Qt.MouseButton.LeftButton:
            index = self.indexAt(event.pos())
            if index.isValid() and index.column() == 0:
                # 双击复选框时也进行状态切换
                model = self.model()
                if model:
                    current_state = model.data(index, Qt.ItemDataRole.CheckStateRole)
                    new_state = Qt.CheckState.Unchecked if current_state == Qt.CheckState.Checked else Qt.CheckState.Checked
                    model.setData(index, new_state, Qt.ItemDataRole.CheckStateRole)
                return

        super().mouseDoubleClickEvent(event)

# ============================================================================
# 🎯 9. 表格数据模型模块
# ============================================================================
# 功能描述: 模拟器列表的数据模型，支持复选框和数据展示
# 调用关系: 被QTableView使用，提供数据展示和交互
# 注意事项: 支持复选框交互，数据更新通过信号通知
# 架构合规: 纯数据模型，不包含业务逻辑
# ============================================================================

class EmulatorTableModel(QAbstractTableModel):
    """模拟器表格数据模型 - 纯数据模型，不包含业务逻辑"""

    def __init__(self, parent=None, ui_service=None):
        super().__init__(parent)
        self.headers = ["选择", "索引", "名称", "模拟器状态", "顶层句柄", "绑定句柄", "PID", "心跳状态", "任务状态", "运行日志", "运行时长", "启动时间", "备注"]
        self.emulators = []
        # 🎯 修复架构违规：直接接收UI服务层实例
        self.ui_service = ui_service

    def set_data(self, emulator_data: list):
        """设置表格数据（由外部调用）"""
        try:
            self.beginResetModel()
            self.emulators = emulator_data if emulator_data else []
            self.endResetModel()
        except Exception as e:
            import logging
            logger = logging.getLogger(self.__class__.__name__)
            logger.error(f"设置表格数据失败: {e}")

    def clear_data(self):
        """清空表格数据"""
        self.beginResetModel()
        self.emulators = []
        self.endResetModel()

    def update_single_emulator_status(self, emulator_id: int, new_status: str, additional_data: dict = None):
        """🎯 兼容性方法 - 委托给统一的UI更新管理器"""
        from core.status_converter import update_emulator_status
        return update_emulator_status(emulator_id, new_status, additional_data, table_model=self)

    def update_single_emulator_status_detailed(self, emulator_id: int, new_status: str, detailed_data: dict):
        """🎯 兼容性方法 - 委托给统一的UI更新管理器"""
        from core.status_converter import update_emulator_detailed
        return update_emulator_detailed(emulator_id, new_status, detailed_data, table_model=self)

    def batch_update_emulators(self, updates: list):
        """🎯 兼容性方法 - 委托给统一的UI更新管理器"""
        from core.status_converter import batch_update_emulators
        return batch_update_emulators(updates, table_model=self)

    def _find_emulator_row(self, emulator_id: int) -> int:
        """🎯 查找模拟器在表格中的行号"""
        for row, emulator in enumerate(self.emulators):
            if len(emulator) > 1 and emulator[1] == emulator_id:  # 索引列
                return row
        return -1

    def rowCount(self, parent=QModelIndex()):
        return len(self.emulators)

    def columnCount(self, parent=QModelIndex()):
        return len(self.headers)

    def data(self, index, role=Qt.ItemDataRole.DisplayRole):
        if not index.isValid():
            return None

        row = index.row()
        col = index.column()

        # 第一列是复选框
        if col == 0:
            if role == Qt.ItemDataRole.CheckStateRole:
                return Qt.CheckState.Checked if self.emulators[row][col] else Qt.CheckState.Unchecked
            elif role == Qt.ItemDataRole.DisplayRole:
                return ""  # 不显示文本，只显示复选框
        else:
            if role == Qt.ItemDataRole.DisplayRole:
                return self.emulators[row][col]
            # 🎯 修复：改用文字颜色显示状态，参考参考代码2的实现
            elif role == Qt.ItemDataRole.ForegroundRole and col == 3:  # 模拟器状态列
                # 🎯 修复架构违规：使用UI服务层
                if self.ui_service:
                    status = self.emulators[row][col]
                    return self.ui_service.get_status_color(status)
            # 🎯 为心跳状态列添加文字颜色区分
            elif role == Qt.ItemDataRole.ForegroundRole and col == 7:  # 心跳状态列（移除ADB端口后）
                # 🎯 修复架构违规：使用UI服务层
                if self.ui_service:
                    heartbeat_status = self.emulators[row][col]
                    return self.ui_service.get_task_activity_color(heartbeat_status)

            # 🎯 为任务状态列添加文字颜色区分
            elif role == Qt.ItemDataRole.ForegroundRole and col == 8:  # 任务状态列
                try:
                    from core.status_converter import StatusConverter
                    task_status = self.emulators[row][col]
                    return StatusConverter.get_instagram_dm_status_color(task_status)
                except Exception as e:
                    # 如果获取颜色失败，返回默认颜色
                    from PyQt6.QtGui import QColor
                    return QColor("#333333")

        return None

    def setData(self, index, value, role=Qt.ItemDataRole.EditRole):
        if not index.isValid():
            return False

        row = index.row()
        col = index.column()

        # 修复：正确处理CheckStateRole
        if role == Qt.ItemDataRole.CheckStateRole and col == 0:
            # 直接更新复选框状态，不触发额外信号
            checked = value == Qt.CheckState.Checked
            self.emulators[row][col] = checked

            # 只发射必要的信号
            self.dataChanged.emit(index, index, [Qt.ItemDataRole.CheckStateRole])
            return True

        return False

    def flags(self, index):
        if not index.isValid():
            return Qt.ItemFlag.NoItemFlags

        # 第一列可以选中
        if index.column() == 0:
            return Qt.ItemFlag.ItemIsEnabled | Qt.ItemFlag.ItemIsUserCheckable
        else:
            return Qt.ItemFlag.ItemIsEnabled | Qt.ItemFlag.ItemIsSelectable

    def headerData(self, section, orientation, role=Qt.ItemDataRole.DisplayRole):
        if orientation == Qt.Orientation.Horizontal and role == Qt.ItemDataRole.DisplayRole:
            if 0 <= section < len(self.headers):
                return self.headers[section]
        return None

