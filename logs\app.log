2025-07-28 17:53:41 - root - INFO - 简化日志系统初始化完成
2025-07-28 17:53:41 - main - INFO - 应用程序启动
2025-07-28 17:53:41 - __main__ - INFO - Qt应用程序已创建
2025-07-28 17:53:41 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-28 17:53:41 - __main__ - INFO - 统一配置管理器已创建
2025-07-28 17:53:41 - DatabaseManager - INFO - SQLite连接已配置WAL模式
2025-07-28 17:53:41 - DatabaseManager - ERROR - 检查表结构失败: 1
2025-07-28 17:53:41 - DatabaseManager - INFO - 数据库表结构创建完成
2025-07-28 17:53:41 - DatabaseManager - INFO - 数据库初始化完成: E:\VScodexiangmu\insleidian2\emulator_system.db
2025-07-28 17:53:41 - EmulatorRepository - INFO - 模拟器数据仓库初始化完成
2025-07-28 17:53:41 - StartupManager - INFO - 统一启动管理器初始化完成
2025-07-28 17:53:41 - TaskActivityHeartbeatManager - INFO - 心跳检测已启用
2025-07-28 17:53:41 - TaskActivityHeartbeatManager - INFO - 任务活动检测心跳管理器初始化完成
2025-07-28 17:53:41 - TaskActivityHeartbeatManager - INFO - 心跳监控服务已启动，检测间隔: 20秒
2025-07-28 17:53:41 - UnifiedEmulatorManager - INFO - 统一模拟器管理器初始化完成
2025-07-28 17:53:41 - __main__ - INFO - 修复版异步桥梁已创建
2025-07-28 17:53:42 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-28 17:53:42 - BasicConfigUI - INFO - 基础配置信号连接完成
2025-07-28 17:53:42 - BasicConfigUI - INFO - 基础配置UI已注册为配置观察者，支持热加载
2025-07-28 17:53:42 - BasicConfigUI - INFO - 浏览按钮事件连接完成
2025-07-28 17:53:42 - BasicConfigUI - INFO - 基础配置UI初始化完成
2025-07-28 17:53:42 - InstagramDMUI - INFO - Instagram私信配置加载完成
2025-07-28 17:53:42 - InstagramFollowUI - INFO - Instagram关注配置加载完成
2025-07-28 17:53:42 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 程序启动)
2025-07-28 17:53:42 - MainWindowV2 - INFO - 事件驱动日志更新已启动 - 表格运行日志列实时更新（替代定时器）
2025-07-28 17:53:42 - MainWindowV2 - INFO - Instagram任务状态更新信号已连接
2025-07-28 17:53:42 - __main__ - INFO - UI主窗口已创建
2025-07-28 17:53:42 - MainWindowV2 - INFO - UI主窗口已创建
2025-07-28 17:53:42 - __main__ - INFO - 主窗口已显示
2025-07-28 17:53:42 - WindowArrangementManager - INFO - 窗口排列管理器初始化完成
2025-07-28 17:53:42 - FixedAsyncBridge - INFO - 任务完成信号已连接到窗口排列管理器
2025-07-28 17:53:42 - FixedAsyncBridge - INFO - 任务完成信号、状态变化信号、心跳监控信号和窗口排列信号已连接到UI
2025-07-28 17:53:42 - FixedAsyncBridge - INFO - 采用事件驱动方式，无需监控器
2025-07-28 17:53:42 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-28 17:53:42 - AsyncEventLoopManager - INFO - 专用异步事件循环已启动
2025-07-28 17:53:42 - __main__ - INFO - UI层和业务层已连接
2025-07-28 17:53:42 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-28 17:53:42 - __main__ - INFO - 启动Qt事件循环
2025-07-28 17:53:42 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-28 17:53:42 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-28 17:53:42 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 17:53:42 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 路径变化)
2025-07-28 17:53:42 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-28 17:53:42 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-28 17:53:42 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-28 17:53:42 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-28 17:53:42 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 17:53:42 - App - INFO - ldconsole命令执行成功，输出长度: 48411
2025-07-28 17:53:42 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-28 17:53:42 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-28 17:53:42 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-28 17:53:42 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-28 17:53:42 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.28s | count: 1229
2025-07-28 17:53:42 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-28 17:53:42 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-28 17:53:42 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-28 17:53:42 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-28 17:53:43 - App - INFO - ldconsole命令执行成功，输出长度: 48411
2025-07-28 17:53:43 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-28 17:53:43 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-28 17:53:43 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-28 17:53:43 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-28 17:53:43 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.25s | count: 1229
2025-07-28 17:53:43 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-28 17:53:43 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-28 17:53:43 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-28 17:53:43 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-28 17:53:43 - ConfigHotReloadService - INFO - 配置热加载服务初始化完成（Qt定时器模式）
2025-07-28 17:53:43 - ConfigHotReloadService - INFO - 配置热加载服务已启动（Qt定时器模式）
2025-07-28 17:53:43 - FixedAsyncBridge - INFO - 后台服务已启动
2025-07-28 17:53:43 - __main__ - INFO - 后台服务已启动
2025-07-28 17:53:43 - __main__ - INFO - 延迟启动服务完成
2025-07-28 17:53:47 - FixedAsyncBridge - INFO - 执行异步操作: instagram_dm_task
2025-07-28 17:53:47 - FixedAsyncBridge - INFO - 异步桥接器: 处理Instagram粉丝私信任务请求
2025-07-28 17:53:47 - MainWindowV2 - INFO - Instagram粉丝私信任务已启动，涉及4个模拟器
2025-07-28 17:53:47 - FixedAsyncBridge - INFO - 开始处理Instagram粉丝私信任务（线程池模式），模拟器数量: 4
2025-07-28 17:53:47 - MainWindowV2 - INFO - 用户启动Instagram粉丝私信任务，模拟器数量: 4
2025-07-28 17:53:47 - FixedAsyncBridge - INFO - 使用批量启动功能启动模拟器: [3, 4, 5, 6]
2025-07-28 17:53:47 - FixedAsyncBridge - INFO - 异步桥接器: 处理批量启动请求，模拟器数量: 4
2025-07-28 17:53:47 - StartupManager - INFO - 批量启动请求 | count: 4
2025-07-28 17:53:47 - StartupManager - INFO - 启动调度器已启动
2025-07-28 17:53:47 - FixedAsyncBridge - INFO - 异步桥接器: 批量启动请求已处理，状态: queued
2025-07-28 17:53:47 - FixedAsyncBridge - INFO - 模拟器批量启动成功，开始创建任务线程池
2025-07-28 17:53:47 - InstagramTaskManager - INFO - Instagram任务管理器已连接模拟器状态变化信号
2025-07-28 17:53:47 - InstagramTaskManager - INFO - Instagram任务管理器初始化完成，最大并发: 2
2025-07-28 17:53:47 - InstagramTaskManager - INFO - 开始启动Instagram任务管理器，模拟器数量: 4
2025-07-28 17:53:47 - InstagramTaskManager - INFO - 线程池创建完成: 成功创建4个Instagram线程
2025-07-28 17:53:47 - FixedAsyncBridge - INFO - 异步桥接器: Instagram粉丝私信任务请求已处理，状态: started
2025-07-28 17:53:47 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器3状态变化: 排队中 -> 启动中
2025-07-28 17:53:47 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 3
2025-07-28 17:53:47 - MainWindowV2 - INFO - 4个模拟器: 未知 -> 排队中
2025-07-28 17:53:47 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 4 | queued: 4 | starting: 0 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 0 | max_concurrent: 2
2025-07-28 17:53:47 - MainWindowV2 - INFO - 启动进度: 排队4个, 启动中0个, 运行0个, 失败0个 (并发槽位:0/2)
2025-07-28 17:53:47 - FixedAsyncBridge - INFO - 异步操作完成: start_batch
2025-07-28 17:53:47 - MainWindowV2 - INFO - 批量启动完成: 0/4 成功
2025-07-28 17:53:47 - MainWindowV2 - INFO - 批量启动操作完成 | success: 0 | total: 4
2025-07-28 17:53:47 - FixedAsyncBridge - INFO - 异步操作完成: instagram_dm_task
2025-07-28 17:53:47 - MainWindowV2 - INFO - Instagram粉丝私信任务启动成功: Instagram粉丝私信任务已启动，涉及4个模拟器（线程池并发模式）
2025-07-28 17:53:47 - Emulator - INFO - 模拟器状态变化 | emulator_id: 3 | old_state: 排队中 | new_state: 启动中
2025-07-28 17:53:47 - MainWindowV2 - INFO - 模拟器3: 排队中 -> 启动中
2025-07-28 17:53:47 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 4 | queued: 3 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 2
2025-07-28 17:53:47 - MainWindowV2 - INFO - 启动进度: 排队3个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/2)
2025-07-28 17:53:47 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 3
2025-07-28 17:53:53 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器4状态变化: 排队中 -> 启动中
2025-07-28 17:53:53 - Emulator - INFO - 模拟器状态变化 | emulator_id: 4 | old_state: 排队中 | new_state: 启动中
2025-07-28 17:53:53 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 4
2025-07-28 17:53:53 - MainWindowV2 - INFO - 模拟器4: 排队中 -> 启动中
2025-07-28 17:53:53 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 4 | queued: 2 | starting: 2 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 2 | max_concurrent: 2
2025-07-28 17:53:53 - MainWindowV2 - INFO - 启动进度: 排队2个, 启动中2个, 运行0个, 失败0个 (并发槽位:2/2)
2025-07-28 17:53:53 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 4
2025-07-28 17:53:58 - Emulator - INFO - Android系统启动完成 | emulator_id: 3 | elapsed_time: 10.2秒
2025-07-28 17:53:58 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器3状态变化: 启动中 -> 运行中
2025-07-28 17:53:58 - InstagramTaskManager - INFO - 启动模拟器3的Instagram私信任务线程 - 当前并发: 1/2
2025-07-28 17:53:58 - Emulator - INFO - 模拟器状态变化 | emulator_id: 3 | old_state: 启动中 | new_state: 运行中
2025-07-28 17:53:58 - Emulator - INFO - 模拟器启动成功 | emulator_id: 3 | running_count: 1
2025-07-28 17:53:58 - TaskActivityHeartbeatManager - INFO - 模拟器 3 已添加到任务活动监控，失败计数: 0
2025-07-28 17:53:58 - MainWindowV2 - INFO - 任务完成: 模拟器3, 任务start
2025-07-28 17:53:58 - MainWindowV2 - INFO - 模拟器3启动成功
2025-07-28 17:53:58 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=3, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 17:53:58 - WindowArrangementManager - INFO - 模拟器3启动完成，立即触发窗口排列
2025-07-28 17:53:58 - InstagramTaskThread - INFO - [模拟器3] 开始等待启动完成
2025-07-28 17:53:58 - MainWindowV2 - WARNING - 未找到模拟器3，无法更新状态
2025-07-28 17:53:58 - InstagramTaskThread - INFO - [模拟器3] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 17:53:58 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中
2025-07-28 17:53:58 - InstagramTaskThread - INFO - [模拟器3] 开始窗口排列
2025-07-28 17:53:58 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 4 | queued: 2 | starting: 1 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 25.0 | concurrent_slots_used: 2 | max_concurrent: 2
2025-07-28 17:53:58 - MainWindowV2 - INFO - 启动进度: 排队2个, 启动中1个, 运行1个, 失败0个 (并发槽位:2/2)
2025-07-28 17:53:58 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中, PID: 22532
2025-07-28 17:53:58 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中
2025-07-28 17:54:00 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 17:54:00 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 17:54:00 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-28 17:54:00 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-28 17:54:00 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 17:54:00 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 17:54:00 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-28 17:54:00 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-28 17:54:00 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-28 17:54:00 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-28 17:54:00 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-28 17:54:00 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-28 17:54:01 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 17:54:01 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 17:54:01 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-28 17:54:01 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-28 17:54:01 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 17:54:03 - Emulator - INFO - Android系统启动完成 | emulator_id: 4 | elapsed_time: 10.1秒
2025-07-28 17:54:03 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器4状态变化: 启动中 -> 运行中
2025-07-28 17:54:03 - InstagramTaskManager - INFO - 启动模拟器4的Instagram私信任务线程 - 当前并发: 2/2
2025-07-28 17:54:03 - Emulator - INFO - 模拟器状态变化 | emulator_id: 4 | old_state: 启动中 | new_state: 运行中
2025-07-28 17:54:03 - Emulator - INFO - 模拟器启动成功 | emulator_id: 4 | running_count: 2
2025-07-28 17:54:03 - TaskActivityHeartbeatManager - INFO - 模拟器 4 已添加到任务活动监控，失败计数: 0
2025-07-28 17:54:03 - MainWindowV2 - INFO - 任务完成: 模拟器4, 任务start
2025-07-28 17:54:03 - MainWindowV2 - INFO - 模拟器4启动成功
2025-07-28 17:54:03 - InstagramTaskThread - INFO - [模拟器4] 开始等待启动完成
2025-07-28 17:54:03 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=4, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 17:54:03 - InstagramTaskThread - INFO - [模拟器4] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 17:54:03 - WindowArrangementManager - INFO - 模拟器4启动完成，立即触发窗口排列
2025-07-28 17:54:03 - InstagramTaskThread - INFO - [模拟器4] 开始窗口排列
2025-07-28 17:54:03 - MainWindowV2 - WARNING - 未找到模拟器4，无法更新状态
2025-07-28 17:54:03 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中
2025-07-28 17:54:03 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 4 | queued: 2 | starting: 0 | running: 2 | failed: 0 | cancelled: 0 | completed: 2 | percentage: 50.0 | concurrent_slots_used: 2 | max_concurrent: 2
2025-07-28 17:54:03 - MainWindowV2 - INFO - 启动进度: 排队2个, 启动中0个, 运行2个, 失败0个 (并发槽位:2/2)
2025-07-28 17:54:03 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中, PID: 19256
2025-07-28 17:54:03 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中
2025-07-28 17:54:05 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 17:54:05 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 17:54:05 - WindowArrangementManager - INFO - 窗口扫描完成，找到 2 个运行中的模拟器窗口
2025-07-28 17:54:05 - WindowArrangementManager - INFO - 找到 2 个正在运行的模拟器窗口
2025-07-28 17:54:05 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 17:54:05 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 17:54:05 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-4' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-28 17:54:05 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 已在网格位置 (0, 0)，保持不动
2025-07-28 17:54:05 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-28 17:54:05 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-28 17:54:05 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-4' 已排列到网格位置 (0, 1): (302, 0) 保持原大小: 302x435
2025-07-28 17:54:05 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/2 个窗口
2025-07-28 17:54:05 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/2 个窗口 (成功: 1/2)
2025-07-28 17:54:06 - InstagramTaskThread - INFO - [模拟器3] 窗口排列完成
2025-07-28 17:54:06 - InstagramTaskThread - INFO - [模拟器3] 开始执行Instagram私信任务
2025-07-28 17:54:06 - InstagramDMTask - INFO - [模拟器3] 雷电模拟器API初始化成功
2025-07-28 17:54:06 - InstagramDMTask - INFO - [模拟器3] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 17:54:06 - InstagramDMTask - INFO - [模拟器3] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 17:54:06 - InstagramDMTask - INFO - [模拟器3] 已设置ld.emulator_id = 3
2025-07-28 17:54:06 - InstagramDMTask - INFO - [模拟器3] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 17:54:06 - InstagramDMTask - INFO - [模拟器3] Instagram任务配置热加载观察者已注册
2025-07-28 17:54:06 - InstagramDMTask - INFO - [模拟器3] Instagram私信任务执行器初始化完成
2025-07-28 17:54:06 - InstagramTaskThread - INFO - [模拟器3] 任务超时计时已从线程启动开始: 666秒
2025-07-28 17:54:06 - InstagramDMTask - INFO - [模拟器3] 开始执行Instagram私信任务
2025-07-28 17:54:06 - InstagramDMTask - INFO - [模拟器3] 任务超时设置: 666秒，已运行: 8.13秒
2025-07-28 17:54:06 - InstagramDMTask - INFO - [模拟器3] 模拟器Android系统运行正常，桌面稳定
2025-07-28 17:54:06 - InstagramDMTask - INFO - [模拟器3] 开始检查应用安装状态
2025-07-28 17:54:06 - InstagramDMTask - INFO - [模拟器3] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 17:54:06 - InstagramDMTask - INFO - [模拟器3] ✅ Instagram已安装，版本: 278.0.0.21.117
2025-07-28 17:54:06 - InstagramDMTask - INFO - [模拟器3] 📊 应用安装状态检测结果:
2025-07-28 17:54:06 - InstagramDMTask - INFO - [模拟器3] ✅ 所有必要应用已安装
2025-07-28 17:54:06 - InstagramDMTask - INFO - [模拟器3] 开始启动V2Ray应用
2025-07-28 17:54:06 - InstagramDMTask - INFO - [模拟器3] V2Ray启动命令执行成功，等待应用加载
2025-07-28 17:54:06 - InstagramDMTask - INFO - [模拟器3] V2Ray应用启动结果: 成功
2025-07-28 17:54:09 - InstagramDMTask - INFO - [模拟器3] ✅ V2Ray应用启动成功
2025-07-28 17:54:09 - InstagramDMTask - INFO - [模拟器3] 开始检查V2Ray节点列表状态
2025-07-28 17:54:11 - InstagramDMTask - INFO - [模拟器3] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 17:54:11 - InstagramDMTask - INFO - [模拟器3] 开始连接V2Ray节点
2025-07-28 17:54:11 - InstagramTaskThread - INFO - [模拟器4] 窗口排列完成
2025-07-28 17:54:11 - InstagramTaskThread - INFO - [模拟器4] 开始执行Instagram私信任务
2025-07-28 17:54:11 - InstagramDMTask - INFO - [模拟器4] 雷电模拟器API初始化成功
2025-07-28 17:54:11 - InstagramDMTask - INFO - [模拟器4] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 17:54:11 - InstagramDMTask - INFO - [模拟器4] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 17:54:11 - InstagramDMTask - INFO - [模拟器4] 已设置ld.emulator_id = 4
2025-07-28 17:54:11 - InstagramDMTask - INFO - [模拟器4] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 17:54:11 - InstagramDMTask - INFO - [模拟器4] Instagram任务配置热加载观察者已注册
2025-07-28 17:54:11 - InstagramDMTask - INFO - [模拟器4] Instagram私信任务执行器初始化完成
2025-07-28 17:54:11 - InstagramTaskThread - INFO - [模拟器4] 任务超时计时已从线程启动开始: 666秒
2025-07-28 17:54:11 - InstagramDMTask - INFO - [模拟器4] 开始执行Instagram私信任务
2025-07-28 17:54:11 - InstagramDMTask - INFO - [模拟器4] 任务超时设置: 666秒，已运行: 8.01秒
2025-07-28 17:54:11 - InstagramDMTask - INFO - [模拟器4] 模拟器Android系统运行正常，桌面稳定
2025-07-28 17:54:11 - InstagramDMTask - INFO - [模拟器4] 开始检查应用安装状态
2025-07-28 17:54:11 - InstagramDMTask - INFO - [模拟器4] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 17:54:11 - InstagramDMTask - INFO - [模拟器4] ✅ Instagram已安装，版本: 321.0.0.39.106
2025-07-28 17:54:11 - InstagramDMTask - INFO - [模拟器4] 📊 应用安装状态检测结果:
2025-07-28 17:54:11 - InstagramDMTask - INFO - [模拟器4] ✅ 所有必要应用已安装
2025-07-28 17:54:11 - InstagramDMTask - INFO - [模拟器4] 开始启动V2Ray应用
2025-07-28 17:54:12 - InstagramDMTask - INFO - [模拟器4] V2Ray启动命令执行成功，等待应用加载
2025-07-28 17:54:12 - InstagramDMTask - INFO - [模拟器4] V2Ray应用启动结果: 成功
2025-07-28 17:54:12 - InstagramDMTask - INFO - [模拟器3] 当前连接状态: 未连接
2025-07-28 17:54:12 - InstagramDMTask - INFO - [模拟器3] V2Ray节点未连接，开始连接
2025-07-28 17:54:13 - InstagramDMTask - INFO - [模拟器3] 已点击连接按钮，等待连接完成
2025-07-28 17:54:15 - InstagramDMTask - INFO - [模拟器4] ✅ V2Ray应用启动成功
2025-07-28 17:54:15 - InstagramDMTask - INFO - [模拟器4] 开始检查V2Ray节点列表状态
2025-07-28 17:54:15 - InstagramDMTask - INFO - [模拟器3] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-28 17:54:15 - InstagramDMTask - INFO - [模拟器3] V2Ray节点连接成功
2025-07-28 17:54:15 - InstagramDMTask - INFO - [模拟器3] 开始测试V2Ray节点延迟
2025-07-28 17:54:15 - InstagramDMTask - INFO - [模拟器3] 开始V2Ray节点延迟测试
2025-07-28 17:54:16 - InstagramDMTask - INFO - [模拟器4] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 17:54:16 - InstagramDMTask - INFO - [模拟器4] 开始连接V2Ray节点
2025-07-28 17:54:17 - InstagramDMTask - INFO - [模拟器3] 当前测试状态: 已连接，点击测试连接
2025-07-28 17:54:17 - InstagramDMTask - INFO - [模拟器3] 点击开始延迟测试
2025-07-28 17:54:17 - InstagramDMTask - INFO - [模拟器3] 已点击测试按钮，等待测试结果
2025-07-28 17:54:17 - InstagramDMTask - INFO - [模拟器4] 当前连接状态: 未连接
2025-07-28 17:54:17 - InstagramDMTask - INFO - [模拟器4] V2Ray节点未连接，开始连接
2025-07-28 17:54:18 - InstagramDMTask - INFO - [模拟器3] 测试状态监控 (1/30): 连接成功：延时 263 毫秒
2025-07-28 17:54:18 - InstagramDMTask - INFO - [模拟器3] ✅ V2Ray节点延迟测试成功: 连接成功：延时 263 毫秒
2025-07-28 17:54:18 - InstagramDMTask - INFO - [模拟器3] 等待5秒后进入下一阶段
2025-07-28 17:54:18 - InstagramDMTask - INFO - [模拟器4] 已点击连接按钮，等待连接完成
2025-07-28 17:54:21 - InstagramDMTask - INFO - [模拟器4] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-28 17:54:21 - InstagramDMTask - INFO - [模拟器4] V2Ray节点连接成功
2025-07-28 17:54:21 - InstagramDMTask - INFO - [模拟器4] 开始测试V2Ray节点延迟
2025-07-28 17:54:21 - InstagramDMTask - INFO - [模拟器4] 开始V2Ray节点延迟测试
2025-07-28 17:54:21 - TaskActivityHeartbeatManager - INFO - 开始检测 2 个模拟器的心跳状态
2025-07-28 17:54:21 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 17:54:21 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-28 17:54:21 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-28 17:54:21 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 4
2025-07-28 17:54:21 - MainWindowV2 - WARNING - 模拟器4心跳状态更新未产生变化
2025-07-28 17:54:21 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 17:54:22 - InstagramDMTask - INFO - [模拟器4] 当前测试状态: 已连接，点击测试连接
2025-07-28 17:54:22 - InstagramDMTask - INFO - [模拟器4] 点击开始延迟测试
2025-07-28 17:54:22 - InstagramDMTask - INFO - [模拟器4] 已点击测试按钮，等待测试结果
2025-07-28 17:54:23 - InstagramDMTask - INFO - [模拟器3] 开始启动Instagram应用
2025-07-28 17:54:23 - InstagramDMTask - INFO - [模拟器4] 测试状态监控 (1/30): 测试中…
2025-07-28 17:54:23 - InstagramDMTask - INFO - [模拟器4] 🔄 延迟测试进行中，继续等待...
2025-07-28 17:54:23 - InstagramDMTask - INFO - [模拟器3] Instagram启动命令执行成功，等待应用加载
2025-07-28 17:54:25 - InstagramDMTask - INFO - [模拟器4] ✅ V2Ray节点延迟测试成功: 连接成功：延时 1533 毫秒
2025-07-28 17:54:25 - InstagramDMTask - INFO - [模拟器4] 等待5秒后进入下一阶段
2025-07-28 17:54:26 - InstagramDMTask - INFO - [模拟器3] ✅ Instagram应用启动命令执行完成
2025-07-28 17:54:26 - InstagramDMTask - INFO - [模拟器3] Instagram启动检测 第1/5次
2025-07-28 17:54:30 - InstagramDMTask - INFO - [模拟器4] 开始启动Instagram应用
2025-07-28 17:54:30 - InstagramDMTask - INFO - [模拟器4] Instagram启动命令执行成功，等待应用加载
2025-07-28 17:54:31 - InstagramDMTask - INFO - [模拟器3] ✅ 批量验证成功
2025-07-28 17:54:31 - InstagramDMTask - INFO - [模拟器3] ✅ 验证成功
2025-07-28 17:54:31 - InstagramDMTask - INFO - [模拟器3] Instagram页面状态检测结果: 正常-在主页面
2025-07-28 17:54:31 - InstagramDMTask - INFO - [模拟器3] ✅ Instagram已在主页面，可以继续执行任务
2025-07-28 17:54:31 - InstagramDMTask - INFO - [模拟器3] 开始导航到个人主页
2025-07-28 17:54:33 - InstagramDMTask - INFO - [模拟器4] ✅ Instagram应用启动命令执行完成
2025-07-28 17:54:33 - InstagramDMTask - INFO - [模拟器4] Instagram启动检测 第1/5次
2025-07-28 17:54:35 - InstagramDMTask - INFO - [模拟器3] ✅ 个人主页加载完成
2025-07-28 17:54:35 - InstagramDMTask - INFO - [模拟器3] 开始获取粉丝数量信息
2025-07-28 17:54:36 - InstagramDMTask - INFO - [模拟器3] 原始粉丝数文本: 6
2025-07-28 17:54:36 - InstagramDMTask - INFO - [模拟器3] ✅ 粉丝数量验证通过: 6
2025-07-28 17:54:36 - InstagramDMTask - INFO - [模拟器3] 开始打开粉丝列表
2025-07-28 17:54:38 - InstagramDMTask - INFO - [模拟器4] Instagram启动检测中... 已等待5.2秒
2025-07-28 17:54:41 - InstagramDMTask - INFO - [模拟器4] ✅ 批量验证成功
2025-07-28 17:54:41 - InstagramDMTask - INFO - [模拟器4] ✅ 验证成功
2025-07-28 17:54:41 - InstagramDMTask - INFO - [模拟器4] Instagram页面状态检测结果: 正常-在主页面
2025-07-28 17:54:41 - InstagramDMTask - INFO - [模拟器4] ✅ Instagram已在主页面，可以继续执行任务
2025-07-28 17:54:41 - InstagramDMTask - INFO - [模拟器4] 开始导航到个人主页
2025-07-28 17:54:41 - InstagramDMTask - INFO - [模拟器3] ✅ 粉丝列表加载完成
2025-07-28 17:54:41 - InstagramDMTask - INFO - [模拟器3] 开始初始化私信任务
2025-07-28 17:54:41 - InstagramDMTask - INFO - [模拟器3] 已加载 9 条去重记录
2025-07-28 17:54:41 - InstagramDMTask - INFO - [模拟器3] 任务目标数量: 25
2025-07-28 17:54:41 - InstagramDMTask - INFO - [模拟器3] 延迟范围: 2-200ms
2025-07-28 17:54:41 - InstagramDMTask - INFO - [模拟器3] ✅ 私信任务初始化完成
2025-07-28 17:54:41 - InstagramDMTask - INFO - [模拟器3] Instagram任务状态更新信号已发送: 私信中（已发送0/目标25）
2025-07-28 17:54:41 - MainWindowV2 - INFO - 模拟器3的Instagram任务状态已更新: 私信中（已发送0/目标25）
2025-07-28 17:54:41 - InstagramDMTask - INFO - [模拟器3] 开始批量私信发送循环
2025-07-28 17:54:41 - InstagramDMTask - INFO - [模拟器3] 初始化循环参数
2025-07-28 17:54:41 - InstagramDMTask - INFO - [模拟器3] 开始主循环，目标数量: 25
2025-07-28 17:54:41 - InstagramDMTask - INFO - [模拟器3] 当前进度: 0/25
2025-07-28 17:54:41 - InstagramDMTask - INFO - [模拟器3] 开始批量获取当前屏幕可见的粉丝列表
2025-07-28 17:54:41 - TaskActivityHeartbeatManager - INFO - 开始检测 2 个模拟器的心跳状态
2025-07-28 17:54:41 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 17:54:41 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-28 17:54:41 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-28 17:54:41 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 4
2025-07-28 17:54:41 - MainWindowV2 - WARNING - 模拟器4心跳状态更新未产生变化
2025-07-28 17:54:41 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 17:54:42 - InstagramDMTask - INFO - [模拟器3] 找到 6 个粉丝
2025-07-28 17:54:42 - InstagramDMTask - INFO - [模拟器3] ✅ 找到符合条件的粉丝: wavytvfawvwa
2025-07-28 17:54:42 - InstagramDMTask - INFO - [模拟器3] ✅ 找到符合条件的粉丝: wavtvawavtva59
2025-07-28 17:54:42 - InstagramDMTask - INFO - [模拟器3] ✅ 找到符合条件的粉丝: dawvtwavtvadawvt
2025-07-28 17:54:42 - InstagramDMTask - INFO - [模拟器3] ✅ 找到符合条件的粉丝: wavtvawavrav
2025-07-28 17:54:42 - InstagramDMTask - INFO - [模拟器3] ✅ 找到符合条件的粉丝: trawvaawvtva
2025-07-28 17:54:42 - InstagramDMTask - INFO - [模拟器3] ✅ 找到符合条件的粉丝: awdvtvdwahad
2025-07-28 17:54:42 - InstagramDMTask - INFO - [模拟器3] 找到 6 个可见粉丝
2025-07-28 17:54:42 - InstagramDMTask - INFO - [模拟器3] 用户 wavytvfawvwa 已发送过，跳过
2025-07-28 17:54:42 - InstagramDMTask - INFO - [模拟器3] 用户 wavtvawavtva59 已发送过，跳过
2025-07-28 17:54:42 - InstagramDMTask - INFO - [模拟器3] 用户 dawvtwavtvadawvt 已发送过，跳过
2025-07-28 17:54:42 - InstagramDMTask - INFO - [模拟器3] 用户 wavtvawavrav 已发送过，跳过
2025-07-28 17:54:42 - InstagramDMTask - INFO - [模拟器3] 📤 准备发送私信给: trawvaawvtva (第1个)
2025-07-28 17:54:42 - InstagramDMTask - INFO - [模拟器3] 开始向 trawvaawvtva 发送私信
2025-07-28 17:54:42 - InstagramDMTask - INFO - [模拟器3] 点击用户名进入主页
2025-07-28 17:54:42 - InstagramDMTask - INFO - [模拟器3] ✅ 点击用户名成功
2025-07-28 17:54:42 - InstagramDMTask - INFO - [模拟器3] 智能等待发消息按钮出现
2025-07-28 17:54:45 - InstagramDMTask - INFO - [模拟器3] ⚡ 快速检测成功: 2.99s, 检测1次
2025-07-28 17:54:45 - InstagramDMTask - INFO - [模拟器4] ✅ 个人主页加载完成
2025-07-28 17:54:45 - InstagramDMTask - INFO - [模拟器4] 开始获取粉丝数量信息
2025-07-28 17:54:45 - InstagramDMTask - INFO - [模拟器3] 撤回功能未启用，跳过撤回步骤
2025-07-28 17:54:45 - InstagramDMTask - INFO - [模拟器3] 智能等待输入框出现
2025-07-28 17:54:47 - InstagramDMTask - INFO - [模拟器4] 原始粉丝数文本: 5
2025-07-28 17:54:47 - InstagramDMTask - INFO - [模拟器4] ✅ 粉丝数量验证通过: 5
2025-07-28 17:54:47 - InstagramDMTask - INFO - [模拟器4] 开始打开粉丝列表
2025-07-28 17:54:48 - InstagramDMTask - INFO - [模拟器3] ⚡ 快速检测成功: 2.22s, 检测1次
2025-07-28 17:54:48 - InstagramDMTask - INFO - [模拟器3] 智能等待输入框激活
2025-07-28 17:54:49 - InstagramDMTask - INFO - [模拟器3] ⚡ 快速检测成功: 1.24s, 检测1次
2025-07-28 17:54:49 - InstagramDMTask - INFO - [模拟器3] ✅ 私信内容输入成功
2025-07-28 17:54:49 - InstagramDMTask - INFO - [模拟器3] 等待消息延迟: 100ms
2025-07-28 17:54:51 - InstagramDMTask - INFO - [模拟器4] ✅ 粉丝列表加载完成
2025-07-28 17:54:51 - InstagramDMTask - INFO - [模拟器4] 开始初始化私信任务
2025-07-28 17:54:51 - InstagramDMTask - INFO - [模拟器4] 已加载 6 条去重记录
2025-07-28 17:54:51 - InstagramDMTask - INFO - [模拟器4] 任务目标数量: 25
2025-07-28 17:54:51 - InstagramDMTask - INFO - [模拟器4] 延迟范围: 2-200ms
2025-07-28 17:54:51 - InstagramDMTask - INFO - [模拟器4] ✅ 私信任务初始化完成
2025-07-28 17:54:51 - InstagramDMTask - INFO - [模拟器4] Instagram任务状态更新信号已发送: 私信中（已发送0/目标25）
2025-07-28 17:54:51 - MainWindowV2 - INFO - 模拟器4的Instagram任务状态已更新: 私信中（已发送0/目标25）
2025-07-28 17:54:51 - InstagramDMTask - INFO - [模拟器4] 开始批量私信发送循环
2025-07-28 17:54:51 - InstagramDMTask - INFO - [模拟器4] 初始化循环参数
2025-07-28 17:54:51 - InstagramDMTask - INFO - [模拟器4] 开始主循环，目标数量: 25
2025-07-28 17:54:51 - InstagramDMTask - INFO - [模拟器4] 当前进度: 0/25
2025-07-28 17:54:51 - InstagramDMTask - INFO - [模拟器4] 开始批量获取当前屏幕可见的粉丝列表
2025-07-28 17:54:52 - InstagramDMTask - INFO - [模拟器3] ✅ 找到发送按钮
2025-07-28 17:54:52 - InstagramDMTask - INFO - [模拟器3] 开始返回粉丝列表（需要2次返回）
2025-07-28 17:54:52 - InstagramDMTask - INFO - [模拟器3] 第1次返回: 私信界面 → 用户主页
2025-07-28 17:54:53 - InstagramDMTask - INFO - [模拟器4] 找到 5 个粉丝
2025-07-28 17:54:53 - InstagramDMTask - INFO - [模拟器4] ✅ 找到符合条件的粉丝: sakura.houkann.cocoa
2025-07-28 17:54:53 - InstagramDMTask - INFO - [模拟器4] ✅ 找到符合条件的粉丝: dawvtwavtvadawvt
2025-07-28 17:54:53 - InstagramDMTask - INFO - [模拟器4] ✅ 找到符合条件的粉丝: wavtvawavtva242
2025-07-28 17:54:53 - InstagramDMTask - INFO - [模拟器4] ✅ 找到符合条件的粉丝: awdvtvdwahad
2025-07-28 17:54:53 - InstagramDMTask - INFO - [模拟器4] ✅ 找到符合条件的粉丝: wavtvawavrav
2025-07-28 17:54:53 - InstagramDMTask - INFO - [模拟器4] 找到 5 个可见粉丝
2025-07-28 17:54:53 - InstagramDMTask - INFO - [模拟器4] 用户 sakura.houkann.cocoa 已发送过，跳过
2025-07-28 17:54:53 - InstagramDMTask - INFO - [模拟器4] 用户 dawvtwavtvadawvt 已发送过，跳过
2025-07-28 17:54:53 - InstagramDMTask - INFO - [模拟器4] 📤 准备发送私信给: wavtvawavtva242 (第1个)
2025-07-28 17:54:53 - InstagramDMTask - INFO - [模拟器4] 开始向 wavtvawavtva242 发送私信
2025-07-28 17:54:53 - InstagramDMTask - INFO - [模拟器4] 点击用户名进入主页
2025-07-28 17:54:53 - InstagramDMTask - INFO - [模拟器4] ✅ 点击用户名成功
2025-07-28 17:54:53 - InstagramDMTask - INFO - [模拟器4] 智能等待发消息按钮出现
2025-07-28 17:54:54 - InstagramDMTask - INFO - [模拟器3] ✅ 返回操作成功 (title_container)
2025-07-28 17:54:54 - InstagramDMTask - INFO - [模拟器3] 智能等待用户主页加载
2025-07-28 17:54:55 - InstagramDMTask - WARNING - [模拟器3] 快速检测超时: 1s, 共检测1次
2025-07-28 17:54:55 - InstagramDMTask - WARNING - [模拟器3] 用户主页加载检测超时，继续执行
2025-07-28 17:54:55 - InstagramDMTask - INFO - [模拟器3] 第2次返回: 用户主页 → 粉丝列表
2025-07-28 17:54:56 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 3.08s, 检测1次
2025-07-28 17:54:56 - InstagramDMTask - INFO - [模拟器4] 撤回功能未启用，跳过撤回步骤
2025-07-28 17:54:56 - InstagramDMTask - INFO - [模拟器4] 智能等待输入框出现
2025-07-28 17:54:57 - InstagramDMTask - INFO - [模拟器3] ✅ 返回操作成功 (back_button)
2025-07-28 17:54:57 - InstagramDMTask - INFO - [模拟器3] 智能等待粉丝列表加载
2025-07-28 17:54:58 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 2.15s, 检测1次
2025-07-28 17:54:58 - InstagramDMTask - INFO - [模拟器4] 智能等待输入框激活
2025-07-28 17:54:58 - InstagramDMTask - INFO - [模拟器3] ⚡ 快速检测成功: 1.30s, 检测1次
2025-07-28 17:54:58 - InstagramDMTask - INFO - [模拟器3] ✅ 成功返回粉丝列表
2025-07-28 17:54:58 - InstagramDMTask - INFO - [模拟器3] ✅ 成功向 trawvaawvtva 发送私信
2025-07-28 17:54:58 - InstagramDMTask - INFO - [模拟器3] ✅ 成功发送第 1 条私信给 trawvaawvtva
2025-07-28 17:54:58 - InstagramDMTask - INFO - [模拟器3] 已记录用户: trawvaawvtva
2025-07-28 17:54:58 - InstagramDMTask - INFO - [模拟器3] Instagram任务状态更新信号已发送: 私信中（已发送1/目标25）
2025-07-28 17:54:58 - InstagramDMTask - INFO - [模拟器3] ⏱️ 等待 70ms 后继续下一个用户
2025-07-28 17:54:58 - MainWindowV2 - INFO - 模拟器3的Instagram任务状态已更新: 私信中（已发送1/目标25）
2025-07-28 17:54:58 - InstagramDMTask - INFO - [模拟器3] 用户 awdvtvdwahad 已发送过，跳过
2025-07-28 17:55:00 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 1.27s, 检测1次
2025-07-28 17:55:00 - InstagramDMTask - INFO - [模拟器4] ✅ 私信内容输入成功
2025-07-28 17:55:00 - InstagramDMTask - INFO - [模拟器4] 等待消息延迟: 100ms
2025-07-28 17:55:01 - InstagramDMTask - INFO - [模拟器3] 滚动粉丝列表
2025-07-28 17:55:01 - TaskActivityHeartbeatManager - INFO - 开始检测 2 个模拟器的心跳状态
2025-07-28 17:55:01 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 17:55:01 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-28 17:55:01 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-28 17:55:01 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 4
2025-07-28 17:55:01 - MainWindowV2 - WARNING - 模拟器4心跳状态更新未产生变化
2025-07-28 17:55:01 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 17:55:02 - InstagramDMUI - INFO - 手动刷新全局统计完成
2025-07-28 17:55:02 - InstagramDMTask - INFO - [模拟器4] ✅ 找到发送按钮
2025-07-28 17:55:02 - InstagramDMTask - INFO - [模拟器3] 滑动6个容器中的5个位置，距离250px
2025-07-28 17:55:03 - InstagramDMUI - INFO - 刷新Instagram私信任务状态
2025-07-28 17:55:03 - InstagramDMTask - INFO - [模拟器4] 开始返回粉丝列表（需要2次返回）
2025-07-28 17:55:03 - InstagramDMTask - INFO - [模拟器4] 第1次返回: 私信界面 → 用户主页
2025-07-28 17:55:04 - InstagramDMTask - INFO - [模拟器3] 当前进度: 1/25
2025-07-28 17:55:04 - InstagramDMTask - INFO - [模拟器3] 开始批量获取当前屏幕可见的粉丝列表
2025-07-28 17:55:05 - InstagramDMTask - INFO - [模拟器4] ✅ 返回操作成功 (title_container)
2025-07-28 17:55:05 - InstagramDMTask - INFO - [模拟器4] 智能等待用户主页加载
2025-07-28 17:55:06 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 1.24s, 检测1次
2025-07-28 17:55:06 - InstagramDMTask - INFO - [模拟器4] 第2次返回: 用户主页 → 粉丝列表
2025-07-28 17:55:06 - InstagramDMTask - INFO - [模拟器3] 找到 3 个粉丝
2025-07-28 17:55:06 - InstagramDMTask - INFO - [模拟器3] ✅ 找到符合条件的粉丝: trawvaawvtva
2025-07-28 17:55:06 - InstagramDMTask - INFO - [模拟器3] ✅ 找到符合条件的粉丝: awdvtvdwahad
2025-07-28 17:55:06 - InstagramDMTask - INFO - [模拟器3] 找到 2 个可见粉丝
2025-07-28 17:55:06 - InstagramDMTask - INFO - [模拟器3] 用户 trawvaawvtva 已发送过，跳过
2025-07-28 17:55:06 - InstagramDMTask - INFO - [模拟器3] 用户 awdvtvdwahad 已发送过，跳过
2025-07-28 17:55:07 - InstagramDMUI - INFO - 手动刷新全局统计完成
2025-07-28 17:55:07 - InstagramDMTask - INFO - [模拟器4] ✅ 返回操作成功 (back_button)
2025-07-28 17:55:07 - InstagramDMTask - INFO - [模拟器4] 智能等待粉丝列表加载
2025-07-28 17:55:08 - InstagramDMUI - INFO - 刷新Instagram私信任务状态
2025-07-28 17:55:09 - InstagramDMTask - INFO - [模拟器3] 已到达粉丝列表底部
2025-07-28 17:55:09 - InstagramDMTask - INFO - [模拟器3] 已到达粉丝列表底部
2025-07-28 17:55:09 - InstagramDMTask - INFO - [模拟器3] ✅ 批量私信发送完成，共发送 1 条私信
2025-07-28 17:55:09 - InstagramDMTask - INFO - [模拟器3] Instagram任务状态更新信号已发送: 私信中（已发送1/目标25）
2025-07-28 17:55:09 - MainWindowV2 - WARNING - 模拟器3的Instagram任务状态更新失败
2025-07-28 17:55:09 - InstagramDMTask - INFO - [模拟器3] Instagram任务配置观察者已注销
2025-07-28 17:55:09 - InstagramDMTask - INFO - [模拟器3] 开始任务完成后清理工作
2025-07-28 17:55:09 - TaskActivityHeartbeatManager - INFO - 模拟器 3 已从任务活动监控移除
2025-07-28 17:55:09 - InstagramDMTask - INFO - [模拟器3] 心跳监控已移除
2025-07-28 17:55:09 - InstagramDMTask - INFO - [模拟器3] 准备关闭模拟器
2025-07-28 17:55:09 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 3 | remaining_running: 1
2025-07-28 17:55:09 - Emulator - INFO - 模拟器停止成功 | emulator_id: 3
2025-07-28 17:55:09 - InstagramDMTask - INFO - [模拟器3] 模拟器已成功关闭
2025-07-28 17:55:09 - MainWindowV2 - INFO - 任务完成: 模拟器3, 任务stop
2025-07-28 17:55:09 - InstagramDMTask - INFO - [模拟器3] 任务完成后清理工作完成
2025-07-28 17:55:09 - MainWindowV2 - INFO - 模拟器3停止成功
2025-07-28 17:55:09 - InstagramTaskThread - INFO - [模拟器3] Instagram任务执行成功: Instagram私信任务执行完成
2025-07-28 17:55:09 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=3, 任务类型=stop, 结果={'result': '停止成功', 'message': ''}
2025-07-28 17:55:09 - WindowArrangementManager - INFO - 跳过非启动任务或失败任务: 任务类型=stop, 结果=停止成功
2025-07-28 17:55:09 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-28 17:55:09 - Emulator - INFO - 模拟器状态变化 | emulator_id: 5 | old_state: 排队中 | new_state: 启动中
2025-07-28 17:55:09 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 5
2025-07-28 17:55:09 - MainWindowV2 - INFO - 模拟器5: 排队中 -> 启动中
2025-07-28 17:55:09 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 4 | queued: 1 | starting: 1 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 25.0 | concurrent_slots_used: 2 | max_concurrent: 2
2025-07-28 17:55:09 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中1个, 运行1个, 失败0个 (并发槽位:2/2)
2025-07-28 17:55:09 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 1.30s, 检测1次
2025-07-28 17:55:09 - InstagramDMTask - INFO - [模拟器4] ✅ 成功返回粉丝列表
2025-07-28 17:55:09 - InstagramDMTask - INFO - [模拟器4] ✅ 成功向 wavtvawavtva242 发送私信
2025-07-28 17:55:09 - InstagramDMTask - INFO - [模拟器4] ✅ 成功发送第 1 条私信给 wavtvawavtva242
2025-07-28 17:55:09 - InstagramDMTask - INFO - [模拟器4] 已记录用户: wavtvawavtva242
2025-07-28 17:55:09 - InstagramDMTask - INFO - [模拟器4] Instagram任务状态更新信号已发送: 私信中（已发送1/目标25）
2025-07-28 17:55:09 - MainWindowV2 - INFO - 模拟器4的Instagram任务状态已更新: 私信中（已发送1/目标25）
2025-07-28 17:55:09 - InstagramDMTask - INFO - [模拟器4] ⏱️ 等待 198ms 后继续下一个用户
2025-07-28 17:55:09 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 5
2025-07-28 17:55:09 - InstagramDMTask - INFO - [模拟器4] 用户 awdvtvdwahad 已发送过，跳过
2025-07-28 17:55:09 - InstagramDMTask - INFO - [模拟器4] 📤 准备发送私信给: wavtvawavrav (第2个)
2025-07-28 17:55:09 - InstagramDMTask - INFO - [模拟器4] 开始向 wavtvawavrav 发送私信
2025-07-28 17:55:09 - InstagramDMTask - INFO - [模拟器4] 点击用户名进入主页
2025-07-28 17:55:09 - InstagramDMTask - INFO - [模拟器4] ✅ 点击用户名成功
2025-07-28 17:55:09 - InstagramDMTask - INFO - [模拟器4] 智能等待发消息按钮出现
2025-07-28 17:55:12 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 3.16s, 检测1次
2025-07-28 17:55:12 - InstagramDMTask - INFO - [模拟器4] 撤回功能未启用，跳过撤回步骤
2025-07-28 17:55:12 - InstagramDMTask - INFO - [模拟器4] 智能等待输入框出现
2025-07-28 17:55:14 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 1.89s, 检测1次
2025-07-28 17:55:15 - InstagramDMTask - INFO - [模拟器4] 智能等待输入框激活
2025-07-28 17:55:16 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 1.27s, 检测1次
2025-07-28 17:55:16 - InstagramDMTask - INFO - [模拟器4] ✅ 私信内容输入成功
2025-07-28 17:55:16 - InstagramDMTask - INFO - [模拟器4] 等待消息延迟: 100ms
2025-07-28 17:55:18 - InstagramDMTask - INFO - [模拟器4] ✅ 找到发送按钮
2025-07-28 17:55:19 - InstagramDMTask - INFO - [模拟器4] 开始返回粉丝列表（需要2次返回）
2025-07-28 17:55:19 - InstagramDMTask - INFO - [模拟器4] 第1次返回: 私信界面 → 用户主页
2025-07-28 17:55:19 - Emulator - INFO - Android系统启动完成 | emulator_id: 5 | elapsed_time: 10.2秒
2025-07-28 17:55:19 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器5状态变化: 启动中 -> 运行中
2025-07-28 17:55:19 - InstagramTaskManager - INFO - 启动模拟器5的Instagram私信任务线程 - 当前并发: 3/2
2025-07-28 17:55:19 - Emulator - INFO - 模拟器状态变化 | emulator_id: 5 | old_state: 启动中 | new_state: 运行中
2025-07-28 17:55:19 - Emulator - INFO - 模拟器启动成功 | emulator_id: 5 | running_count: 2
2025-07-28 17:55:19 - TaskActivityHeartbeatManager - INFO - 模拟器 5 已添加到任务活动监控，失败计数: 0
2025-07-28 17:55:19 - MainWindowV2 - INFO - 任务完成: 模拟器5, 任务start
2025-07-28 17:55:19 - MainWindowV2 - INFO - 模拟器5启动成功
2025-07-28 17:55:19 - InstagramTaskThread - INFO - [模拟器5] 开始等待启动完成
2025-07-28 17:55:19 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=5, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 17:55:19 - InstagramTaskThread - INFO - [模拟器5] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 17:55:19 - InstagramTaskThread - INFO - [模拟器5] 开始窗口排列
2025-07-28 17:55:19 - WindowArrangementManager - INFO - 模拟器5启动完成，立即触发窗口排列
2025-07-28 17:55:19 - MainWindowV2 - WARNING - 未找到模拟器5，无法更新状态
2025-07-28 17:55:19 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中
2025-07-28 17:55:19 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 4 | queued: 1 | starting: 0 | running: 2 | failed: 0 | cancelled: 0 | completed: 2 | percentage: 50.0 | concurrent_slots_used: 2 | max_concurrent: 2
2025-07-28 17:55:19 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中0个, 运行2个, 失败0个 (并发槽位:2/2)
2025-07-28 17:55:19 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中, PID: 26492
2025-07-28 17:55:19 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中
2025-07-28 17:55:21 - InstagramDMTask - INFO - [模拟器4] ✅ 返回操作成功 (title_container)
2025-07-28 17:55:21 - InstagramDMTask - INFO - [模拟器4] 智能等待用户主页加载
2025-07-28 17:55:21 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 17:55:21 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 17:55:21 - WindowArrangementManager - INFO - 窗口扫描完成，找到 2 个运行中的模拟器窗口
2025-07-28 17:55:21 - WindowArrangementManager - INFO - 找到 2 个正在运行的模拟器窗口
2025-07-28 17:55:21 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 17:55:21 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 17:55:21 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-5' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-28 17:55:21 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-4' 已在网格位置 (0, 1)，保持不动
2025-07-28 17:55:21 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-28 17:55:21 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-28 17:55:21 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-5' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-28 17:55:21 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/2 个窗口
2025-07-28 17:55:21 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/2 个窗口 (成功: 1/2)
2025-07-28 17:55:21 - TaskActivityHeartbeatManager - INFO - 开始检测 2 个模拟器的心跳状态
2025-07-28 17:55:21 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 17:55:21 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 4
2025-07-28 17:55:21 - MainWindowV2 - WARNING - 模拟器4心跳状态更新未产生变化
2025-07-28 17:55:21 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 5
2025-07-28 17:55:21 - MainWindowV2 - WARNING - 模拟器5心跳状态更新未产生变化
2025-07-28 17:55:21 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 17:55:22 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 1.22s, 检测1次
2025-07-28 17:55:22 - InstagramDMTask - INFO - [模拟器4] 第2次返回: 用户主页 → 粉丝列表
2025-07-28 17:55:24 - InstagramDMTask - INFO - [模拟器4] ✅ 返回操作成功 (back_button)
2025-07-28 17:55:24 - InstagramDMTask - INFO - [模拟器4] 智能等待粉丝列表加载
2025-07-28 17:55:25 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 1.29s, 检测1次
2025-07-28 17:55:25 - InstagramDMTask - INFO - [模拟器4] ✅ 成功返回粉丝列表
2025-07-28 17:55:25 - InstagramDMTask - INFO - [模拟器4] ✅ 成功向 wavtvawavrav 发送私信
2025-07-28 17:55:25 - InstagramDMTask - INFO - [模拟器4] ✅ 成功发送第 2 条私信给 wavtvawavrav
2025-07-28 17:55:25 - InstagramDMTask - INFO - [模拟器4] 已记录用户: wavtvawavrav
2025-07-28 17:55:25 - InstagramDMTask - INFO - [模拟器4] Instagram任务状态更新信号已发送: 私信中（已发送2/目标25）
2025-07-28 17:55:25 - InstagramDMTask - INFO - [模拟器4] ⏱️ 等待 122ms 后继续下一个用户
2025-07-28 17:55:25 - MainWindowV2 - INFO - 模拟器4的Instagram任务状态已更新: 私信中（已发送2/目标25）
2025-07-28 17:55:27 - InstagramTaskThread - INFO - [模拟器5] 窗口排列完成
2025-07-28 17:55:27 - InstagramTaskThread - INFO - [模拟器5] 开始执行Instagram私信任务
2025-07-28 17:55:27 - InstagramDMTask - INFO - [模拟器5] 雷电模拟器API初始化成功
2025-07-28 17:55:27 - InstagramDMTask - INFO - [模拟器5] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 17:55:27 - InstagramDMTask - INFO - [模拟器5] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 17:55:27 - InstagramDMTask - INFO - [模拟器5] 已设置ld.emulator_id = 5
2025-07-28 17:55:27 - InstagramDMTask - INFO - [模拟器5] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 17:55:27 - InstagramDMTask - INFO - [模拟器5] Instagram任务配置热加载观察者已注册
2025-07-28 17:55:27 - InstagramDMTask - INFO - [模拟器5] Instagram私信任务执行器初始化完成
2025-07-28 17:55:27 - InstagramTaskThread - INFO - [模拟器5] 任务超时计时已从线程启动开始: 666秒
2025-07-28 17:55:27 - InstagramDMTask - INFO - [模拟器5] 开始执行Instagram私信任务
2025-07-28 17:55:27 - InstagramDMTask - INFO - [模拟器5] 任务超时设置: 666秒，已运行: 8.00秒
2025-07-28 17:55:27 - InstagramDMTask - INFO - [模拟器5] 模拟器Android系统运行正常，桌面稳定
2025-07-28 17:55:27 - InstagramDMTask - INFO - [模拟器5] 开始检查应用安装状态
2025-07-28 17:55:28 - InstagramDMTask - INFO - [模拟器5] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 17:55:28 - InstagramDMTask - INFO - [模拟器5] ✅ Instagram已安装，版本: 278.0.0.21.117
2025-07-28 17:55:28 - InstagramDMTask - INFO - [模拟器5] 📊 应用安装状态检测结果:
2025-07-28 17:55:28 - InstagramDMTask - INFO - [模拟器5] ✅ 所有必要应用已安装
2025-07-28 17:55:28 - InstagramDMTask - INFO - [模拟器5] 开始启动V2Ray应用
2025-07-28 17:55:28 - InstagramDMTask - INFO - [模拟器4] 已到达粉丝列表底部
2025-07-28 17:55:28 - InstagramDMTask - INFO - [模拟器4] 已到达粉丝列表底部
2025-07-28 17:55:28 - InstagramDMTask - INFO - [模拟器4] ✅ 批量私信发送完成，共发送 2 条私信
2025-07-28 17:55:28 - InstagramDMTask - INFO - [模拟器4] Instagram任务状态更新信号已发送: 私信中（已发送2/目标25）
2025-07-28 17:55:28 - InstagramDMTask - INFO - [模拟器4] Instagram任务配置观察者已注销
2025-07-28 17:55:28 - InstagramDMTask - INFO - [模拟器4] 开始任务完成后清理工作
2025-07-28 17:55:28 - TaskActivityHeartbeatManager - INFO - 模拟器 4 已从任务活动监控移除
2025-07-28 17:55:28 - InstagramDMTask - INFO - [模拟器4] 心跳监控已移除
2025-07-28 17:55:28 - InstagramDMTask - INFO - [模拟器4] 准备关闭模拟器
2025-07-28 17:55:28 - MainWindowV2 - WARNING - 模拟器4的Instagram任务状态更新失败
2025-07-28 17:55:28 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 4 | remaining_running: 1
2025-07-28 17:55:28 - Emulator - INFO - 模拟器停止成功 | emulator_id: 4
2025-07-28 17:55:28 - MainWindowV2 - INFO - 任务完成: 模拟器4, 任务stop
2025-07-28 17:55:28 - InstagramDMTask - INFO - [模拟器4] 模拟器已成功关闭
2025-07-28 17:55:28 - MainWindowV2 - INFO - 模拟器4停止成功
2025-07-28 17:55:28 - InstagramDMTask - INFO - [模拟器4] 任务完成后清理工作完成
2025-07-28 17:55:28 - InstagramTaskThread - INFO - [模拟器4] Instagram任务执行成功: Instagram私信任务执行完成
2025-07-28 17:55:28 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=4, 任务类型=stop, 结果={'result': '停止成功', 'message': ''}
2025-07-28 17:55:28 - WindowArrangementManager - INFO - 跳过非启动任务或失败任务: 任务类型=stop, 结果=停止成功
2025-07-28 17:55:28 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器6状态变化: 排队中 -> 启动中
2025-07-28 17:55:28 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 6
2025-07-28 17:55:28 - Emulator - INFO - 模拟器状态变化 | emulator_id: 6 | old_state: 排队中 | new_state: 启动中
2025-07-28 17:55:28 - MainWindowV2 - INFO - 模拟器6: 排队中 -> 启动中
2025-07-28 17:55:28 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 4 | queued: 0 | starting: 1 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 25.0 | concurrent_slots_used: 2 | max_concurrent: 2
2025-07-28 17:55:28 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中1个, 运行1个, 失败0个 (并发槽位:2/2)
2025-07-28 17:55:28 - InstagramDMTask - INFO - [模拟器5] V2Ray启动命令执行成功，等待应用加载
2025-07-28 17:55:28 - InstagramDMTask - INFO - [模拟器5] V2Ray应用启动结果: 成功
2025-07-28 17:55:28 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 6
2025-07-28 17:55:31 - InstagramDMTask - INFO - [模拟器5] ✅ V2Ray应用启动成功
2025-07-28 17:55:31 - InstagramDMTask - INFO - [模拟器5] 开始检查V2Ray节点列表状态
2025-07-28 17:55:32 - InstagramDMTask - INFO - [模拟器5] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 17:55:32 - InstagramDMTask - INFO - [模拟器5] 开始连接V2Ray节点
2025-07-28 17:55:33 - InstagramDMTask - INFO - [模拟器5] 当前连接状态: 未连接
2025-07-28 17:55:33 - InstagramDMTask - INFO - [模拟器5] V2Ray节点未连接，开始连接
2025-07-28 17:55:35 - InstagramDMTask - INFO - [模拟器5] 已点击连接按钮，等待连接完成
2025-07-28 17:55:37 - InstagramDMTask - INFO - [模拟器5] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-28 17:55:37 - InstagramDMTask - INFO - [模拟器5] V2Ray节点连接成功
2025-07-28 17:55:37 - InstagramDMTask - INFO - [模拟器5] 开始测试V2Ray节点延迟
2025-07-28 17:55:37 - InstagramDMTask - INFO - [模拟器5] 开始V2Ray节点延迟测试
2025-07-28 17:55:37 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 17:55:37 - __main__ - INFO - 开始清理资源...
2025-07-28 17:55:37 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 17:55:37 - __main__ - INFO - 配置已保存
2025-07-28 17:55:37 - __main__ - ERROR - 异步桥梁关闭失败: 'FixedAsyncBridge' object has no attribute 'shutdown'
2025-07-28 17:55:37 - __main__ - INFO - 应用程序已退出
