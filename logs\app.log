2025-07-28 18:21:51 - root - INFO - 简化日志系统初始化完成
2025-07-28 18:21:51 - main - INFO - 应用程序启动
2025-07-28 18:21:51 - __main__ - INFO - Qt应用程序已创建
2025-07-28 18:21:52 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-28 18:21:52 - __main__ - INFO - 统一配置管理器已创建
2025-07-28 18:21:52 - DatabaseManager - INFO - SQLite连接已配置WAL模式
2025-07-28 18:21:52 - DatabaseManager - ERROR - 检查表结构失败: 1
2025-07-28 18:21:52 - DatabaseManager - INFO - 数据库表结构创建完成
2025-07-28 18:21:52 - DatabaseManager - INFO - 数据库初始化完成: E:\VScodexiangmu\insleidian2\emulator_system.db
2025-07-28 18:21:52 - EmulatorRepository - INFO - 模拟器数据仓库初始化完成
2025-07-28 18:21:52 - StartupManager - INFO - 统一启动管理器初始化完成
2025-07-28 18:21:52 - TaskActivityHeartbeatManager - INFO - 心跳检测已启用
2025-07-28 18:21:52 - TaskActivityHeartbeatManager - INFO - 任务活动检测心跳管理器初始化完成
2025-07-28 18:21:52 - TaskActivityHeartbeatManager - INFO - 心跳监控服务已启动，检测间隔: 20秒
2025-07-28 18:21:52 - UnifiedEmulatorManager - INFO - 统一模拟器管理器初始化完成
2025-07-28 18:21:52 - __main__ - INFO - 修复版异步桥梁已创建
2025-07-28 18:21:52 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-28 18:21:52 - BasicConfigUI - INFO - 基础配置信号连接完成
2025-07-28 18:21:52 - BasicConfigUI - INFO - 基础配置UI已注册为配置观察者，支持热加载
2025-07-28 18:21:52 - BasicConfigUI - INFO - 浏览按钮事件连接完成
2025-07-28 18:21:52 - BasicConfigUI - INFO - 基础配置UI初始化完成
2025-07-28 18:21:52 - InstagramDMUI - INFO - Instagram私信配置加载完成
2025-07-28 18:21:52 - InstagramFollowUI - INFO - Instagram关注配置加载完成
2025-07-28 18:21:52 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 程序启动)
2025-07-28 18:21:52 - MainWindowV2 - INFO - 事件驱动日志更新已启动 - 表格运行日志列实时更新（替代定时器）
2025-07-28 18:21:52 - MainWindowV2 - INFO - Instagram任务状态更新信号已连接
2025-07-28 18:21:52 - __main__ - INFO - UI主窗口已创建
2025-07-28 18:21:52 - MainWindowV2 - INFO - UI主窗口已创建
2025-07-28 18:21:52 - __main__ - INFO - 主窗口已显示
2025-07-28 18:21:52 - WindowArrangementManager - INFO - 窗口排列管理器初始化完成
2025-07-28 18:21:52 - FixedAsyncBridge - INFO - 任务完成信号已连接到窗口排列管理器
2025-07-28 18:21:52 - FixedAsyncBridge - INFO - 任务完成信号、状态变化信号、心跳监控信号和窗口排列信号已连接到UI
2025-07-28 18:21:52 - FixedAsyncBridge - INFO - 采用事件驱动方式，无需监控器
2025-07-28 18:21:52 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-28 18:21:52 - AsyncEventLoopManager - INFO - 专用异步事件循环已启动
2025-07-28 18:21:52 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-28 18:21:52 - __main__ - INFO - UI层和业务层已连接
2025-07-28 18:21:52 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-28 18:21:52 - __main__ - INFO - 启动Qt事件循环
2025-07-28 18:21:52 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-28 18:21:52 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 18:21:53 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 路径变化)
2025-07-28 18:21:53 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-28 18:21:53 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-28 18:21:53 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-28 18:21:53 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-28 18:21:53 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 18:21:53 - App - INFO - ldconsole命令执行成功，输出长度: 48450
2025-07-28 18:21:53 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-28 18:21:53 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-28 18:21:53 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-28 18:21:53 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-28 18:21:53 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.39s | count: 1229
2025-07-28 18:21:53 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-28 18:21:53 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-28 18:21:53 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-28 18:21:53 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-28 18:21:53 - App - INFO - ldconsole命令执行成功，输出长度: 48450
2025-07-28 18:21:53 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-28 18:21:53 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-28 18:21:53 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-28 18:21:53 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-28 18:21:53 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.28s | count: 1229
2025-07-28 18:21:53 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-28 18:21:53 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-28 18:21:53 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-28 18:21:53 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-28 18:21:53 - ConfigHotReloadService - INFO - 配置热加载服务初始化完成（Qt定时器模式）
2025-07-28 18:21:53 - ConfigHotReloadService - INFO - 配置热加载服务已启动（Qt定时器模式）
2025-07-28 18:21:53 - FixedAsyncBridge - INFO - 后台服务已启动
2025-07-28 18:21:53 - __main__ - INFO - 后台服务已启动
2025-07-28 18:21:53 - __main__ - INFO - 延迟启动服务完成
2025-07-28 18:21:56 - FixedAsyncBridge - INFO - 执行异步操作: instagram_dm_task
2025-07-28 18:21:56 - MainWindowV2 - INFO - Instagram粉丝私信任务已启动，涉及1个模拟器
2025-07-28 18:21:56 - FixedAsyncBridge - INFO - 异步桥接器: 处理Instagram粉丝私信任务请求
2025-07-28 18:21:56 - MainWindowV2 - INFO - 用户启动Instagram粉丝私信任务，模拟器数量: 1
2025-07-28 18:21:56 - FixedAsyncBridge - INFO - 开始处理Instagram粉丝私信任务（线程池模式），模拟器数量: 1
2025-07-28 18:21:56 - FixedAsyncBridge - INFO - 使用批量启动功能启动模拟器: [6]
2025-07-28 18:21:56 - FixedAsyncBridge - INFO - 异步桥接器: 处理批量启动请求，模拟器数量: 1
2025-07-28 18:21:56 - StartupManager - INFO - 批量启动请求 | count: 1
2025-07-28 18:21:56 - StartupManager - INFO - 启动调度器已启动
2025-07-28 18:21:56 - FixedAsyncBridge - INFO - 异步桥接器: 批量启动请求已处理，状态: queued
2025-07-28 18:21:56 - FixedAsyncBridge - INFO - 模拟器批量启动成功，开始创建任务线程池
2025-07-28 18:21:56 - InstagramTaskManager - INFO - Instagram任务管理器已连接模拟器状态变化信号
2025-07-28 18:21:56 - InstagramTaskManager - INFO - Instagram任务管理器初始化完成，最大并发: 1
2025-07-28 18:21:56 - InstagramTaskManager - INFO - 开始启动Instagram任务管理器，模拟器数量: 1
2025-07-28 18:21:56 - InstagramTaskManager - INFO - 线程池创建完成: 成功创建1个Instagram线程
2025-07-28 18:21:56 - FixedAsyncBridge - INFO - 异步桥接器: Instagram粉丝私信任务请求已处理，状态: started
2025-07-28 18:21:56 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器6状态变化: 排队中 -> 启动中
2025-07-28 18:21:56 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 6
2025-07-28 18:21:56 - MainWindowV2 - INFO - 模拟器6: 未知 -> 排队中
2025-07-28 18:21:56 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 1 | queued: 1 | starting: 0 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 0 | max_concurrent: 1
2025-07-28 18:21:56 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中0个, 运行0个, 失败0个 (并发槽位:0/1)
2025-07-28 18:21:56 - FixedAsyncBridge - INFO - 异步操作完成: start_batch
2025-07-28 18:21:56 - MainWindowV2 - INFO - 批量启动完成: 0/1 成功
2025-07-28 18:21:56 - MainWindowV2 - INFO - 批量启动操作完成 | success: 0 | total: 1
2025-07-28 18:21:56 - FixedAsyncBridge - INFO - 异步操作完成: instagram_dm_task
2025-07-28 18:21:56 - MainWindowV2 - INFO - Instagram粉丝私信任务启动成功: Instagram粉丝私信任务已启动，涉及1个模拟器（线程池并发模式）
2025-07-28 18:21:56 - Emulator - INFO - 模拟器状态变化 | emulator_id: 6 | old_state: 排队中 | new_state: 启动中
2025-07-28 18:21:56 - MainWindowV2 - INFO - 模拟器6: 排队中 -> 启动中
2025-07-28 18:21:56 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 1 | queued: 0 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-28 18:21:56 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/1)
2025-07-28 18:21:56 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 6
2025-07-28 18:22:06 - Emulator - INFO - Android系统启动完成 | emulator_id: 6 | elapsed_time: 9.9秒
2025-07-28 18:22:06 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器6状态变化: 启动中 -> 运行中
2025-07-28 18:22:06 - InstagramTaskManager - INFO - 启动模拟器6的Instagram私信任务线程 - 当前并发: 1/1
2025-07-28 18:22:06 - Emulator - INFO - 模拟器状态变化 | emulator_id: 6 | old_state: 启动中 | new_state: 运行中
2025-07-28 18:22:06 - Emulator - INFO - 模拟器启动成功 | emulator_id: 6 | running_count: 1
2025-07-28 18:22:06 - TaskActivityHeartbeatManager - INFO - 模拟器 6 已添加到任务活动监控，失败计数: 0
2025-07-28 18:22:06 - MainWindowV2 - INFO - 任务完成: 模拟器6, 任务start
2025-07-28 18:22:06 - MainWindowV2 - INFO - 模拟器6启动成功
2025-07-28 18:22:06 - InstagramTaskThread - INFO - [模拟器6] 开始等待启动完成
2025-07-28 18:22:06 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=6, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 18:22:06 - InstagramTaskThread - INFO - [模拟器6] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 18:22:06 - InstagramTaskThread - INFO - [模拟器6] 开始窗口排列
2025-07-28 18:22:06 - WindowArrangementManager - INFO - 模拟器6启动完成，立即触发窗口排列
2025-07-28 18:22:06 - MainWindowV2 - WARNING - 未找到模拟器6，无法更新状态
2025-07-28 18:22:06 - MainWindowV2 - INFO - 模拟器6: 启动中 -> 运行中
2025-07-28 18:22:06 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 1 | queued: 0 | starting: 0 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 100.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-28 18:22:06 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中0个, 运行1个, 失败0个 (并发槽位:1/1)
2025-07-28 18:22:06 - MainWindowV2 - INFO - 模拟器6: 启动中 -> 运行中, PID: 1528
2025-07-28 18:22:06 - MainWindowV2 - INFO - 模拟器6: 启动中 -> 运行中
2025-07-28 18:22:07 - StartupManager - INFO - 调度器已停止
2025-07-28 18:22:08 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 18:22:08 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 18:22:08 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-28 18:22:08 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-28 18:22:08 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 18:22:08 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 18:22:08 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-6' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-28 18:22:08 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-28 18:22:08 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-28 18:22:08 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-6' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-28 18:22:08 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-28 18:22:08 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-28 18:22:12 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 18:22:12 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 18:22:12 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 6
2025-07-28 18:22:12 - MainWindowV2 - WARNING - 模拟器6心跳状态更新未产生变化
2025-07-28 18:22:12 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 18:22:14 - InstagramTaskThread - INFO - [模拟器6] 窗口排列完成
2025-07-28 18:22:14 - InstagramTaskThread - INFO - [模拟器6] 开始执行Instagram私信任务
2025-07-28 18:22:14 - InstagramDMTask - INFO - [模拟器6] 雷电模拟器API初始化成功
2025-07-28 18:22:14 - InstagramDMTask - INFO - [模拟器6] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 18:22:14 - InstagramDMTask - INFO - [模拟器6] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 18:22:14 - InstagramDMTask - INFO - [模拟器6] 已设置ld.emulator_id = 6
2025-07-28 18:22:14 - InstagramDMTask - INFO - [模拟器6] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 18:22:14 - InstagramDMTask - INFO - [模拟器6] Instagram任务配置热加载观察者已注册
2025-07-28 18:22:14 - InstagramDMTask - INFO - [模拟器6] Instagram私信任务执行器初始化完成
2025-07-28 18:22:14 - InstagramTaskThread - INFO - [模拟器6] 任务超时计时已从线程启动开始: 666秒
2025-07-28 18:22:14 - InstagramDMTask - INFO - [模拟器6] 开始执行Instagram私信任务
2025-07-28 18:22:14 - InstagramDMTask - INFO - [模拟器6] 任务超时设置: 666秒，已运行: 8.20秒
2025-07-28 18:22:14 - InstagramDMTask - INFO - [模拟器6] 模拟器Android系统运行正常，桌面稳定
2025-07-28 18:22:14 - InstagramDMTask - INFO - [模拟器6] 开始检查应用安装状态
2025-07-28 18:22:15 - InstagramDMTask - INFO - [模拟器6] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 18:22:15 - InstagramDMTask - INFO - [模拟器6] ✅ Instagram已安装，版本: 321.0.0.39.106
2025-07-28 18:22:15 - InstagramDMTask - INFO - [模拟器6] 📊 应用安装状态检测结果:
2025-07-28 18:22:15 - InstagramDMTask - INFO - [模拟器6] ✅ 所有必要应用已安装
2025-07-28 18:22:15 - InstagramDMTask - INFO - [模拟器6] 开始启动V2Ray应用
2025-07-28 18:22:15 - InstagramDMTask - INFO - [模拟器6] V2Ray启动命令执行成功，等待应用加载
2025-07-28 18:22:15 - InstagramDMTask - INFO - [模拟器6] V2Ray应用启动结果: 成功
2025-07-28 18:22:18 - InstagramDMTask - INFO - [模拟器6] ✅ V2Ray应用启动成功
2025-07-28 18:22:18 - InstagramDMTask - INFO - [模拟器6] 开始检查V2Ray节点列表状态
2025-07-28 18:22:19 - InstagramDMTask - INFO - [模拟器6] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 18:22:19 - InstagramDMTask - INFO - [模拟器6] 开始连接V2Ray节点
2025-07-28 18:22:21 - InstagramDMTask - INFO - [模拟器6] 当前连接状态: 未连接
2025-07-28 18:22:21 - InstagramDMTask - INFO - [模拟器6] V2Ray节点未连接，开始连接
2025-07-28 18:22:22 - InstagramDMTask - INFO - [模拟器6] 已点击连接按钮，等待连接完成
2025-07-28 18:22:24 - InstagramDMTask - INFO - [模拟器6] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-28 18:22:24 - InstagramDMTask - INFO - [模拟器6] V2Ray节点连接成功
2025-07-28 18:22:24 - InstagramDMTask - INFO - [模拟器6] 开始测试V2Ray节点延迟
2025-07-28 18:22:24 - InstagramDMTask - INFO - [模拟器6] 开始V2Ray节点延迟测试
2025-07-28 18:22:25 - InstagramDMTask - INFO - [模拟器6] 当前测试状态: 已连接，点击测试连接
2025-07-28 18:22:25 - InstagramDMTask - INFO - [模拟器6] 点击开始延迟测试
2025-07-28 18:22:26 - InstagramDMTask - INFO - [模拟器6] 已点击测试按钮，等待测试结果
2025-07-28 18:22:27 - InstagramDMTask - INFO - [模拟器6] 测试状态监控 (1/30): 连接成功：延时 525 毫秒
2025-07-28 18:22:27 - InstagramDMTask - INFO - [模拟器6] ✅ V2Ray节点延迟测试成功: 连接成功：延时 525 毫秒
2025-07-28 18:22:27 - InstagramDMTask - INFO - [模拟器6] 等待5秒后进入下一阶段
2025-07-28 18:22:32 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 18:22:32 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 18:22:32 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 6
2025-07-28 18:22:32 - MainWindowV2 - WARNING - 模拟器6心跳状态更新未产生变化
2025-07-28 18:22:32 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 18:22:32 - InstagramDMTask - INFO - [模拟器6] 开始启动Instagram应用
2025-07-28 18:22:32 - InstagramDMTask - INFO - [模拟器6] Instagram启动命令执行成功，等待应用加载
2025-07-28 18:22:35 - InstagramDMTask - INFO - [模拟器6] ✅ Instagram应用启动命令执行完成
2025-07-28 18:22:35 - InstagramDMTask - INFO - [模拟器6] Instagram启动检测 第1/5次
2025-07-28 18:22:40 - InstagramDMTask - INFO - [模拟器6] ✅ 批量验证成功
2025-07-28 18:22:40 - InstagramDMTask - INFO - [模拟器6] ✅ 验证成功
2025-07-28 18:22:40 - InstagramDMTask - INFO - [模拟器6] Instagram页面状态检测结果: 正常-在主页面
2025-07-28 18:22:40 - InstagramDMTask - INFO - [模拟器6] ✅ Instagram已在主页面，可以继续执行任务
2025-07-28 18:22:40 - InstagramDMTask - INFO - [模拟器6] 开始导航到个人主页
2025-07-28 18:22:44 - InstagramDMTask - INFO - [模拟器6] ✅ 个人主页加载完成
2025-07-28 18:22:44 - InstagramDMTask - INFO - [模拟器6] 开始获取粉丝数量信息
2025-07-28 18:22:46 - InstagramDMTask - INFO - [模拟器6] 原始粉丝数文本: 6
2025-07-28 18:22:46 - InstagramDMTask - INFO - [模拟器6] ✅ 粉丝数量验证通过: 6
2025-07-28 18:22:46 - InstagramDMTask - INFO - [模拟器6] 开始打开粉丝列表
2025-07-28 18:22:50 - InstagramDMTask - INFO - [模拟器6] ✅ 粉丝列表加载完成
2025-07-28 18:22:50 - InstagramDMTask - INFO - [模拟器6] 开始初始化私信任务
2025-07-28 18:22:50 - InstagramDMTask - INFO - [模拟器6] 已加载 8 条去重记录
2025-07-28 18:22:50 - InstagramDMTask - INFO - [模拟器6] 任务目标数量: 25
2025-07-28 18:22:50 - InstagramDMTask - INFO - [模拟器6] 延迟范围: 2-200ms
2025-07-28 18:22:50 - InstagramDMTask - INFO - [模拟器6] ✅ 私信任务初始化完成
2025-07-28 18:22:50 - InstagramDMTask - INFO - [模拟器6] Instagram任务状态更新信号已发送: 私信中（已发送0/目标25）
2025-07-28 18:22:50 - InstagramDMTask - INFO - [模拟器6] 开始批量私信发送循环
2025-07-28 18:22:50 - InstagramDMTask - INFO - [模拟器6] 初始化循环参数
2025-07-28 18:22:50 - InstagramDMTask - INFO - [模拟器6] 开始主循环，目标数量: 25
2025-07-28 18:22:50 - InstagramDMTask - INFO - [模拟器6] 当前进度: 0/25
2025-07-28 18:22:50 - InstagramDMTask - INFO - [模拟器6] 开始批量获取当前屏幕可见的粉丝列表
2025-07-28 18:22:50 - MainWindowV2 - INFO - 模拟器6的Instagram任务状态已更新: 私信中（已发送0/目标25）
2025-07-28 18:22:51 - InstagramDMTask - INFO - [模拟器6] 找到 6 个粉丝
2025-07-28 18:22:51 - InstagramDMTask - INFO - [模拟器6] ✅ 找到符合条件的粉丝: wavytvfawvwa
2025-07-28 18:22:51 - InstagramDMTask - INFO - [模拟器6] ✅ 找到符合条件的粉丝: wavtvawavtva59
2025-07-28 18:22:51 - InstagramDMTask - INFO - [模拟器6] ✅ 找到符合条件的粉丝: wavtvawavrav
2025-07-28 18:22:51 - InstagramDMTask - INFO - [模拟器6] ✅ 找到符合条件的粉丝: trawvaawvtva
2025-07-28 18:22:51 - InstagramDMTask - INFO - [模拟器6] ✅ 找到符合条件的粉丝: dawvtwavtvadawvt
2025-07-28 18:22:51 - InstagramDMTask - INFO - [模拟器6] ✅ 找到符合条件的粉丝: wavtvawavtva242
2025-07-28 18:22:51 - InstagramDMTask - INFO - [模拟器6] 找到 6 个可见粉丝
2025-07-28 18:22:51 - InstagramDMTask - INFO - [模拟器6] 📤 准备发送私信给: wavytvfawvwa (第1个)
2025-07-28 18:22:51 - InstagramDMTask - INFO - [模拟器6] 开始向 wavytvfawvwa 发送私信
2025-07-28 18:22:51 - InstagramDMTask - INFO - [模拟器6] 点击用户名进入主页
2025-07-28 18:22:52 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 18:22:52 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 18:22:52 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 6
2025-07-28 18:22:52 - MainWindowV2 - WARNING - 模拟器6心跳状态更新未产生变化
2025-07-28 18:22:52 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 18:22:52 - InstagramDMTask - INFO - [模拟器6] ✅ 点击用户名成功
2025-07-28 18:22:52 - InstagramDMTask - INFO - [模拟器6] 智能等待发消息按钮出现
2025-07-28 18:22:55 - InstagramDMTask - INFO - [模拟器6] ⚡ 快速检测成功: 3.71s, 检测1次
2025-07-28 18:22:55 - InstagramDMTask - INFO - [模拟器6] 撤回功能已启用，开始执行撤回
2025-07-28 18:22:55 - InstagramDMTask - INFO - [模拟器6] 开始执行私信撤回功能（支持多条撤回）
2025-07-28 18:22:58 - InstagramDMTask - INFO - [模拟器6] 未找到消息
2025-07-28 18:22:58 - InstagramDMTask - WARNING - [模拟器6] 第一次未找到消息元素，等待界面加载后重试
2025-07-28 18:23:01 - InstagramDMTask - INFO - [模拟器6] 找到 3 条消息
2025-07-28 18:23:01 - InstagramDMTask - INFO - [模拟器6] 重试成功，找到消息元素
2025-07-28 18:23:01 - InstagramDMTask - INFO - [模拟器6] 找到 3 个消息元素（多种类型），最多尝试撤回 10 条
2025-07-28 18:23:01 - InstagramDMTask - INFO - [模拟器6] 尝试撤回第 3 条消息
2025-07-28 18:23:01 - InstagramDMTask - INFO - [模拟器6] 边界预判断: 右边界=255 >= 239，判定为自己的消息
2025-07-28 18:23:03 - InstagramDMTask - INFO - [模拟器6] 长按消息
2025-07-28 18:23:04 - InstagramDMTask - INFO - [模拟器6] 撤回按钮未找到
2025-07-28 18:23:04 - InstagramDMTask - INFO - [模拟器6] 撤回按钮未找到
2025-07-28 18:23:05 - InstagramDMTask - INFO - [模拟器6] 撤回按钮未找到
2025-07-28 18:23:05 - InstagramDMTask - INFO - [模拟器6] 撤回按钮未找到
2025-07-28 18:23:06 - InstagramDMTask - INFO - [模拟器6] 撤回按钮未找到
2025-07-28 18:23:06 - InstagramDMTask - INFO - [模拟器6] 智能等待超时，可能是对方发送的消息
2025-07-28 18:23:06 - InstagramDMTask - INFO - [模拟器6] 未找到撤回选项，可能是对方的消息
2025-07-28 18:23:06 - InstagramDMTask - INFO - [模拟器6] 对方消息，取消菜单
2025-07-28 18:23:06 - InstagramDMTask - INFO - [模拟器6] 尝试撤回第 2 条消息
2025-07-28 18:23:06 - InstagramDMTask - INFO - [模拟器6] 边界预判断: 右边界=219 < 239，跳过对方消息
2025-07-28 18:23:06 - InstagramDMTask - INFO - [模拟器6] 尝试撤回第 1 条消息
2025-07-28 18:23:06 - InstagramDMTask - INFO - [模拟器6] 边界预判断: 右边界=185 < 239，跳过对方消息
2025-07-28 18:23:06 - InstagramDMTask - INFO - [模拟器6] 撤回流程完成，未找到可撤回的消息
2025-07-28 18:23:06 - InstagramDMTask - INFO - [模拟器6] ✅ 撤回功能执行完成
2025-07-28 18:23:06 - InstagramDMTask - INFO - [模拟器6] 智能等待输入框出现
2025-07-28 18:23:08 - InstagramDMTask - INFO - [模拟器6] ⚡ 快速检测成功: 1.21s, 检测1次
2025-07-28 18:23:08 - InstagramDMTask - INFO - [模拟器6] 智能等待输入框激活
2025-07-28 18:23:09 - InstagramDMTask - INFO - [模拟器6] ⚡ 快速检测成功: 1.25s, 检测1次
2025-07-28 18:23:09 - InstagramDMTask - INFO - [模拟器6] ✅ 私信内容输入成功
2025-07-28 18:23:09 - InstagramDMTask - INFO - [模拟器6] 等待消息延迟: 100ms
2025-07-28 18:23:12 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 18:23:12 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 18:23:12 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 6
2025-07-28 18:23:12 - MainWindowV2 - WARNING - 模拟器6心跳状态更新未产生变化
2025-07-28 18:23:12 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 18:23:12 - InstagramDMTask - INFO - [模拟器6] ✅ 找到发送按钮
2025-07-28 18:23:12 - InstagramDMTask - INFO - [模拟器6] 开始返回粉丝列表（需要2次返回）
2025-07-28 18:23:12 - InstagramDMTask - INFO - [模拟器6] 第1次返回: 私信界面 → 用户主页
2025-07-28 18:23:14 - InstagramDMTask - INFO - [模拟器6] ✅ 返回操作成功 (title_container)
2025-07-28 18:23:14 - InstagramDMTask - INFO - [模拟器6] 智能等待用户主页加载
2025-07-28 18:23:15 - InstagramDMTask - WARNING - [模拟器6] 快速检测超时: 1s, 共检测1次
2025-07-28 18:23:15 - InstagramDMTask - WARNING - [模拟器6] 用户主页加载检测超时，继续执行
2025-07-28 18:23:15 - InstagramDMTask - INFO - [模拟器6] 第2次返回: 用户主页 → 粉丝列表
2025-07-28 18:23:17 - InstagramDMTask - INFO - [模拟器6] ✅ 返回操作成功 (back_button)
2025-07-28 18:23:17 - InstagramDMTask - INFO - [模拟器6] 智能等待粉丝列表加载
2025-07-28 18:23:18 - InstagramDMTask - INFO - [模拟器6] ⚡ 快速检测成功: 1.29s, 检测1次
2025-07-28 18:23:18 - InstagramDMTask - INFO - [模拟器6] ✅ 成功返回粉丝列表
2025-07-28 18:23:18 - InstagramDMTask - INFO - [模拟器6] ✅ 成功向 wavytvfawvwa 发送私信
2025-07-28 18:23:18 - InstagramDMTask - INFO - [模拟器6] ✅ 成功发送第 1 条私信给 wavytvfawvwa
2025-07-28 18:23:18 - InstagramDMTask - INFO - [模拟器6] 已记录用户: wavytvfawvwa
2025-07-28 18:23:18 - InstagramDMTask - INFO - [模拟器6] Instagram任务状态更新信号已发送: 私信中（已发送1/目标25）
2025-07-28 18:23:18 - InstagramDMTask - INFO - [模拟器6] ⏱️ 等待 15ms 后继续下一个用户
2025-07-28 18:23:18 - MainWindowV2 - INFO - 模拟器6的Instagram任务状态已更新: 私信中（已发送1/目标25）
2025-07-28 18:23:18 - InstagramDMTask - INFO - [模拟器6] 📤 准备发送私信给: wavtvawavtva59 (第2个)
2025-07-28 18:23:18 - InstagramDMTask - INFO - [模拟器6] 开始向 wavtvawavtva59 发送私信
2025-07-28 18:23:18 - InstagramDMTask - INFO - [模拟器6] 点击用户名进入主页
2025-07-28 18:23:18 - InstagramDMTask - INFO - [模拟器6] ✅ 点击用户名成功
2025-07-28 18:23:18 - InstagramDMTask - INFO - [模拟器6] 智能等待发消息按钮出现
2025-07-28 18:23:22 - InstagramDMTask - INFO - [模拟器6] ⚡ 快速检测成功: 3.43s, 检测1次
2025-07-28 18:23:22 - InstagramDMTask - INFO - [模拟器6] 撤回功能已启用，开始执行撤回
2025-07-28 18:23:22 - InstagramDMTask - INFO - [模拟器6] 开始执行私信撤回功能（支持多条撤回）
2025-07-28 18:23:24 - InstagramDMTask - INFO - [模拟器6] 未找到消息
2025-07-28 18:23:24 - InstagramDMTask - WARNING - [模拟器6] 第一次未找到消息元素，等待界面加载后重试
2025-07-28 18:23:27 - InstagramDMTask - INFO - [模拟器6] 找到 3 条消息
2025-07-28 18:23:27 - InstagramDMTask - INFO - [模拟器6] 重试成功，找到消息元素
2025-07-28 18:23:27 - InstagramDMTask - INFO - [模拟器6] 找到 3 个消息元素（多种类型），最多尝试撤回 10 条
2025-07-28 18:23:27 - InstagramDMTask - INFO - [模拟器6] 尝试撤回第 3 条消息
2025-07-28 18:23:27 - InstagramDMTask - INFO - [模拟器6] 边界预判断: 右边界=255 >= 239，判定为自己的消息
2025-07-28 18:23:29 - InstagramDMTask - INFO - [模拟器6] 长按消息
2025-07-28 18:23:29 - InstagramDMTask - INFO - [模拟器6] ✅ 找到撤回按钮 (置信度:1.000)
2025-07-28 18:23:29 - InstagramDMTask - INFO - [模拟器6] ✅ 撤回选项立即找到
2025-07-28 18:23:29 - InstagramDMTask - INFO - [模拟器6] 找到撤回选项，点击撤回
2025-07-28 18:23:29 - InstagramDMTask - INFO - [模拟器6] 使用图色识别点击撤回: 模板=chehui.png 坐标=(176,384)
2025-07-28 18:23:30 - InstagramDMTask - INFO - [模拟器6] 点击撤回操作: 
2025-07-28 18:23:30 - InstagramDMTask - INFO - [模拟器6] 处理撤回确认弹窗
2025-07-28 18:23:31 - InstagramDMTask - INFO - [模拟器6] ⚡ 快速检测成功: 1.23s, 检测1次
2025-07-28 18:23:31 - InstagramDMTask - INFO - [模拟器6] 发现撤回确认弹窗，点击撤回
2025-07-28 18:23:32 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 18:23:32 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 18:23:32 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 6
2025-07-28 18:23:32 - MainWindowV2 - WARNING - 模拟器6心跳状态更新未产生变化
2025-07-28 18:23:32 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 18:23:35 - InstagramDMTask - INFO - [模拟器6] ⚡ 快速检测成功: 2.67s, 检测2次
2025-07-28 18:23:35 - InstagramDMTask - INFO - [模拟器6] 发现确定弹窗，点击确定
2025-07-28 18:23:35 - InstagramDMTask - INFO - [模拟器6] 撤回确认处理完成
2025-07-28 18:23:37 - InstagramDMTask - INFO - [模拟器6] 重新获取消息元素（撤回后位置已变化）
2025-07-28 18:23:38 - InstagramDMTask - INFO - [模拟器6] 找到 2 条消息
2025-07-28 18:23:38 - InstagramDMTask - INFO - [模拟器6] ✅ 第 1 条消息撤回操作完成，剩余 2 条消息
2025-07-28 18:23:38 - InstagramDMTask - INFO - [模拟器6] 🔍 下一条待撤回消息 - 类型: text_message, 边界: '[35,321][101,344]'
2025-07-28 18:23:38 - InstagramDMTask - INFO - [模拟器6] 尝试撤回第 2 条消息
2025-07-28 18:23:38 - InstagramDMTask - INFO - [模拟器6] 边界预判断: 右边界=101 < 239，跳过对方消息
2025-07-28 18:23:38 - InstagramDMTask - INFO - [模拟器6] 尝试撤回第 1 条消息
2025-07-28 18:23:38 - InstagramDMTask - INFO - [模拟器6] 边界预判断: 右边界=85 < 239，跳过对方消息
2025-07-28 18:23:38 - InstagramDMTask - INFO - [模拟器6] 撤回流程完成，成功撤回 1 条消息
2025-07-28 18:23:38 - InstagramDMTask - INFO - [模拟器6] ✅ 撤回功能执行完成
2025-07-28 18:23:38 - InstagramDMTask - INFO - [模拟器6] 智能等待输入框出现
2025-07-28 18:23:39 - InstagramDMTask - INFO - [模拟器6] ⚡ 快速检测成功: 1.19s, 检测1次
2025-07-28 18:23:40 - InstagramDMTask - INFO - [模拟器6] 智能等待输入框激活
2025-07-28 18:23:41 - InstagramDMTask - INFO - [模拟器6] ⚡ 快速检测成功: 1.24s, 检测1次
2025-07-28 18:23:41 - InstagramDMTask - INFO - [模拟器6] ✅ 私信内容输入成功
2025-07-28 18:23:41 - InstagramDMTask - INFO - [模拟器6] 等待消息延迟: 100ms
2025-07-28 18:23:43 - InstagramDMTask - INFO - [模拟器6] ✅ 找到发送按钮
2025-07-28 18:23:44 - InstagramDMTask - INFO - [模拟器6] 开始返回粉丝列表（需要2次返回）
2025-07-28 18:23:44 - InstagramDMTask - INFO - [模拟器6] 第1次返回: 私信界面 → 用户主页
2025-07-28 18:23:46 - InstagramDMTask - INFO - [模拟器6] ✅ 返回操作成功 (title_container)
2025-07-28 18:23:46 - InstagramDMTask - INFO - [模拟器6] 智能等待用户主页加载
2025-07-28 18:23:47 - InstagramDMTask - INFO - [模拟器6] ⚡ 快速检测成功: 1.21s, 检测1次
2025-07-28 18:23:47 - InstagramDMTask - INFO - [模拟器6] 第2次返回: 用户主页 → 粉丝列表
2025-07-28 18:23:48 - InstagramDMTask - INFO - [模拟器6] ✅ 返回操作成功 (back_button)
2025-07-28 18:23:48 - InstagramDMTask - INFO - [模拟器6] 智能等待粉丝列表加载
2025-07-28 18:23:50 - InstagramDMTask - INFO - [模拟器6] ⚡ 快速检测成功: 1.30s, 检测1次
2025-07-28 18:23:50 - InstagramDMTask - INFO - [模拟器6] ✅ 成功返回粉丝列表
2025-07-28 18:23:50 - InstagramDMTask - INFO - [模拟器6] ✅ 成功向 wavtvawavtva59 发送私信
2025-07-28 18:23:50 - InstagramDMTask - INFO - [模拟器6] ✅ 成功发送第 2 条私信给 wavtvawavtva59
2025-07-28 18:23:50 - InstagramDMTask - INFO - [模拟器6] 已记录用户: wavtvawavtva59
2025-07-28 18:23:50 - InstagramDMTask - INFO - [模拟器6] Instagram任务状态更新信号已发送: 私信中（已发送2/目标25）
2025-07-28 18:23:50 - InstagramDMTask - INFO - [模拟器6] ⏱️ 等待 125ms 后继续下一个用户
2025-07-28 18:23:50 - MainWindowV2 - INFO - 模拟器6的Instagram任务状态已更新: 私信中（已发送2/目标25）
2025-07-28 18:23:50 - InstagramDMTask - INFO - [模拟器6] 用户 wavtvawavrav 已发送过，跳过
2025-07-28 18:23:50 - InstagramDMTask - INFO - [模拟器6] 用户 trawvaawvtva 已发送过，跳过
2025-07-28 18:23:50 - InstagramDMTask - INFO - [模拟器6] 用户 dawvtwavtvadawvt 已发送过，跳过
2025-07-28 18:23:50 - InstagramDMTask - INFO - [模拟器6] 用户 wavtvawavtva242 已发送过，跳过
2025-07-28 18:23:52 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 18:23:52 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 18:23:52 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 6
2025-07-28 18:23:52 - MainWindowV2 - WARNING - 模拟器6心跳状态更新未产生变化
2025-07-28 18:23:52 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 18:23:52 - InstagramDMTask - INFO - [模拟器6] 滚动粉丝列表
2025-07-28 18:23:54 - InstagramDMTask - INFO - [模拟器6] 滑动6个容器中的5个位置，距离250px
2025-07-28 18:23:55 - InstagramDMTask - INFO - [模拟器6] 当前进度: 2/25
2025-07-28 18:23:55 - InstagramDMTask - INFO - [模拟器6] 开始批量获取当前屏幕可见的粉丝列表
2025-07-28 18:23:57 - InstagramDMTask - INFO - [模拟器6] 找到 3 个粉丝
2025-07-28 18:23:57 - InstagramDMTask - INFO - [模拟器6] ✅ 找到符合条件的粉丝: dawvtwavtvadawvt
2025-07-28 18:23:57 - InstagramDMTask - INFO - [模拟器6] ✅ 找到符合条件的粉丝: wavtvawavtva242
2025-07-28 18:23:57 - InstagramDMTask - INFO - [模拟器6] 找到 2 个可见粉丝
2025-07-28 18:23:57 - InstagramDMTask - INFO - [模拟器6] 用户 dawvtwavtvadawvt 已发送过，跳过
2025-07-28 18:23:57 - InstagramDMTask - INFO - [模拟器6] 用户 wavtvawavtva242 已发送过，跳过
2025-07-28 18:23:59 - InstagramDMTask - INFO - [模拟器6] 已到达粉丝列表底部
2025-07-28 18:23:59 - InstagramDMTask - INFO - [模拟器6] 已到达粉丝列表底部
2025-07-28 18:23:59 - InstagramDMTask - INFO - [模拟器6] ✅ 批量私信发送完成，共发送 2 条私信
2025-07-28 18:23:59 - InstagramDMTask - INFO - [模拟器6] Instagram任务状态更新信号已发送: 私信中（已发送2/目标25）
2025-07-28 18:23:59 - InstagramDMTask - INFO - [模拟器6] Instagram任务配置观察者已注销
2025-07-28 18:23:59 - InstagramDMTask - INFO - [模拟器6] 开始任务完成后清理工作
2025-07-28 18:23:59 - TaskActivityHeartbeatManager - INFO - 模拟器 6 已从任务活动监控移除
2025-07-28 18:23:59 - InstagramDMTask - INFO - [模拟器6] 心跳监控已移除
2025-07-28 18:23:59 - InstagramDMTask - INFO - [模拟器6] 准备关闭模拟器
2025-07-28 18:23:59 - MainWindowV2 - WARNING - 模拟器6的Instagram任务状态更新失败
2025-07-28 18:23:59 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 6 | remaining_running: 0
2025-07-28 18:23:59 - Emulator - INFO - 模拟器停止成功 | emulator_id: 6
2025-07-28 18:23:59 - InstagramDMTask - INFO - [模拟器6] 模拟器已成功关闭
2025-07-28 18:23:59 - InstagramDMTask - INFO - [模拟器6] 任务完成后清理工作完成
2025-07-28 18:23:59 - InstagramTaskThread - INFO - [模拟器6] Instagram任务执行成功: Instagram私信任务执行完成
2025-07-28 18:23:59 - MainWindowV2 - INFO - 任务完成: 模拟器6, 任务stop
2025-07-28 18:23:59 - MainWindowV2 - INFO - 模拟器6停止成功
2025-07-28 18:23:59 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=6, 任务类型=stop, 结果={'result': '停止成功', 'message': ''}
2025-07-28 18:23:59 - WindowArrangementManager - INFO - 跳过非启动任务或失败任务: 任务类型=stop, 结果=停止成功
2025-07-28 18:24:09 - FixedAsyncBridge - INFO - 执行异步操作: instagram_follow_direct_task
2025-07-28 18:24:09 - MainWindowV2 - INFO - Instagram直接关注任务已启动，涉及1个模拟器
2025-07-28 18:24:09 - FixedAsyncBridge - INFO - 异步桥接器: 处理Instagram直接关注任务请求
2025-07-28 18:24:09 - MainWindowV2 - INFO - 用户启动Instagram直接关注任务，模拟器数量: 1
2025-07-28 18:24:09 - FixedAsyncBridge - INFO - 开始处理Instagram直接关注任务（线程池模式），模拟器数量: 1
2025-07-28 18:24:09 - FixedAsyncBridge - INFO - 使用批量启动功能启动模拟器: [7]
2025-07-28 18:24:09 - FixedAsyncBridge - INFO - 异步桥接器: 处理批量启动请求，模拟器数量: 1
2025-07-28 18:24:09 - StartupManager - INFO - 批量启动请求 | count: 1
2025-07-28 18:24:09 - StartupManager - INFO - 启动调度器已启动
2025-07-28 18:24:09 - FixedAsyncBridge - INFO - 异步桥接器: 批量启动请求已处理，状态: queued
2025-07-28 18:24:09 - FixedAsyncBridge - INFO - 模拟器批量启动成功，开始创建关注任务线程池
2025-07-28 18:24:09 - InstagramTaskManager - INFO - Instagram任务管理器已连接模拟器状态变化信号
2025-07-28 18:24:09 - InstagramTaskManager - INFO - Instagram任务管理器初始化完成，最大并发: 1
2025-07-28 18:24:09 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器初始化完成，模式: direct
2025-07-28 18:24:09 - InstagramTaskManager - INFO - 开始启动Instagram任务管理器，模拟器数量: 1
2025-07-28 18:24:09 - MainWindowV2 - INFO - 模拟器7: 未知 -> 排队中
2025-07-28 18:24:09 - InstagramFollowTaskThread - INFO - [模拟器7] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 18:24:09 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 1 | queued: 1 | starting: 0 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 0 | max_concurrent: 1
2025-07-28 18:24:09 - InstagramFollowTaskManager - INFO - 为模拟器 7 创建Instagram关注任务线程，模式: direct
2025-07-28 18:24:09 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中0个, 运行0个, 失败0个 (并发槽位:0/1)
2025-07-28 18:24:09 - InstagramTaskManager - INFO - 线程池创建完成: 成功创建1个Instagram线程
2025-07-28 18:24:09 - FixedAsyncBridge - INFO - 异步操作完成: start_batch
2025-07-28 18:24:09 - FixedAsyncBridge - INFO - 异步桥接器: Instagram直接关注任务请求已处理，状态: started
2025-07-28 18:24:09 - MainWindowV2 - INFO - 批量启动完成: 0/1 成功
2025-07-28 18:24:09 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器7状态变化: 排队中 -> 启动中
2025-07-28 18:24:09 - MainWindowV2 - INFO - 批量启动操作完成 | success: 0 | total: 1
2025-07-28 18:24:09 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器7状态变化: 排队中 -> 启动中
2025-07-28 18:24:09 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器7状态变化: 排队中 -> 启动中
2025-07-28 18:24:09 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 7
2025-07-28 18:24:09 - FixedAsyncBridge - INFO - 异步操作完成: instagram_follow_direct_task
2025-07-28 18:24:09 - Emulator - INFO - 模拟器状态变化 | emulator_id: 7 | old_state: 排队中 | new_state: 启动中
2025-07-28 18:24:09 - MainWindowV2 - INFO - 模拟器7: 排队中 -> 启动中
2025-07-28 18:24:09 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 1 | queued: 0 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-28 18:24:09 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/1)
2025-07-28 18:24:09 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 7
2025-07-28 18:24:23 - Emulator - INFO - Android系统启动完成 | emulator_id: 7 | elapsed_time: 13.2秒
2025-07-28 18:24:23 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器7状态变化: 启动中 -> 运行中
2025-07-28 18:24:23 - Emulator - INFO - 模拟器状态变化 | emulator_id: 7 | old_state: 启动中 | new_state: 运行中
2025-07-28 18:24:23 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器7状态变化: 启动中 -> 运行中
2025-07-28 18:24:23 - TaskActivityHeartbeatManager - INFO - 模拟器 7 已添加到任务活动监控，失败计数: 0
2025-07-28 18:24:23 - InstagramFollowTaskManager - INFO - 启动模拟器7的Instagram关注任务线程 - 当前并发: 1/1
2025-07-28 18:24:23 - Emulator - INFO - 模拟器启动成功 | emulator_id: 7 | running_count: 1
2025-07-28 18:24:23 - MainWindowV2 - INFO - 任务完成: 模拟器7, 任务start
2025-07-28 18:24:23 - MainWindowV2 - INFO - 模拟器7启动成功
2025-07-28 18:24:23 - InstagramTaskThread - INFO - [模拟器7] 开始等待启动完成
2025-07-28 18:24:23 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=7, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 18:24:23 - InstagramTaskThread - INFO - [模拟器7] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 18:24:23 - InstagramTaskThread - INFO - [模拟器7] 开始窗口排列
2025-07-28 18:24:23 - WindowArrangementManager - INFO - 模拟器7启动完成，立即触发窗口排列
2025-07-28 18:24:23 - MainWindowV2 - WARNING - 未找到模拟器7，无法更新状态
2025-07-28 18:24:23 - MainWindowV2 - INFO - 模拟器7: 启动中 -> 运行中
2025-07-28 18:24:23 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 1 | queued: 0 | starting: 0 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 100.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-28 18:24:23 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中0个, 运行1个, 失败0个 (并发槽位:1/1)
2025-07-28 18:24:23 - MainWindowV2 - INFO - 模拟器7: 启动中 -> 运行中, PID: 14148
2025-07-28 18:24:23 - MainWindowV2 - INFO - 模拟器7: 启动中 -> 运行中
2025-07-28 18:24:23 - StartupManager - INFO - 调度器已停止
2025-07-28 18:24:25 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 18:24:25 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 18:24:25 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-28 18:24:25 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-28 18:24:25 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 18:24:25 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 18:24:25 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-7' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-28 18:24:25 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-28 18:24:25 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-28 18:24:25 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-7' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-28 18:24:25 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-28 18:24:25 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-28 18:24:31 - InstagramTaskThread - INFO - [模拟器7] 窗口排列完成
2025-07-28 18:24:31 - InstagramFollowTaskThread - INFO - [模拟器7] 开始执行Instagram直接关注任务
2025-07-28 18:24:31 - InstagramDMTask - INFO - [模拟器7] 雷电模拟器API初始化成功
2025-07-28 18:24:31 - InstagramDMTask - INFO - [模拟器7] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 18:24:31 - InstagramDMTask - INFO - [模拟器7] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 18:24:31 - InstagramDMTask - INFO - [模拟器7] 已设置ld.emulator_id = 7
2025-07-28 18:24:31 - InstagramDMTask - INFO - [模拟器7] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 18:24:31 - InstagramDMTask - INFO - [模拟器7] Instagram任务配置热加载观察者已注册
2025-07-28 18:24:31 - InstagramDMTask - INFO - [模拟器7] Instagram私信任务执行器初始化完成
2025-07-28 18:24:31 - InstagramFollowTask - INFO - [模拟器7] Instagram关注任务配置加载完成
2025-07-28 18:24:31 - InstagramFollowTask - INFO - [模拟器7] Instagram关注任务执行器初始化完成
2025-07-28 18:24:31 - InstagramFollowTask - INFO - [模拟器7] 关注模式已设置为: direct
2025-07-28 18:24:31 - InstagramFollowTask - INFO - [模拟器7] 开始执行Instagram关注任务
2025-07-28 18:24:31 - InstagramFollowTask - WARNING - [模拟器7] 任务开始时间未由线程传递，在此设置
2025-07-28 18:24:31 - InstagramFollowTask - INFO - [模拟器7] 任务超时设置: 666秒，已运行: 0.00秒
2025-07-28 18:24:31 - InstagramDMTask - INFO - [模拟器7] 模拟器Android系统运行正常，桌面稳定
2025-07-28 18:24:31 - InstagramDMTask - INFO - [模拟器7] 开始检查应用安装状态
2025-07-28 18:24:31 - InstagramDMTask - INFO - [模拟器7] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 18:24:31 - InstagramDMTask - INFO - [模拟器7] ✅ Instagram已安装，版本: 321.0.0.39.106
2025-07-28 18:24:31 - InstagramDMTask - INFO - [模拟器7] 📊 应用安装状态检测结果:
2025-07-28 18:24:31 - InstagramDMTask - INFO - [模拟器7] ✅ 所有必要应用已安装
2025-07-28 18:24:31 - InstagramDMTask - INFO - [模拟器7] 开始启动V2Ray应用
2025-07-28 18:24:31 - InstagramDMTask - INFO - [模拟器7] V2Ray启动命令执行成功，等待应用加载
2025-07-28 18:24:31 - InstagramDMTask - INFO - [模拟器7] V2Ray应用启动结果: 成功
2025-07-28 18:24:32 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 18:24:32 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 18:24:32 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 7
2025-07-28 18:24:32 - MainWindowV2 - WARNING - 模拟器7心跳状态更新未产生变化
2025-07-28 18:24:32 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 18:24:34 - InstagramDMTask - INFO - [模拟器7] ✅ V2Ray应用启动成功
2025-07-28 18:24:34 - InstagramDMTask - INFO - [模拟器7] 开始检查V2Ray节点列表状态
2025-07-28 18:24:36 - InstagramDMTask - INFO - [模拟器7] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 18:24:36 - InstagramDMTask - INFO - [模拟器7] 开始连接V2Ray节点
2025-07-28 18:24:37 - InstagramDMTask - INFO - [模拟器7] 当前连接状态: 未连接
2025-07-28 18:24:37 - InstagramDMTask - INFO - [模拟器7] V2Ray节点未连接，开始连接
2025-07-28 18:24:38 - InstagramDMTask - INFO - [模拟器7] 已点击连接按钮，等待连接完成
2025-07-28 18:24:41 - InstagramDMTask - INFO - [模拟器7] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-28 18:24:41 - InstagramDMTask - INFO - [模拟器7] V2Ray节点连接成功
2025-07-28 18:24:41 - InstagramDMTask - INFO - [模拟器7] 开始测试V2Ray节点延迟
2025-07-28 18:24:41 - InstagramDMTask - INFO - [模拟器7] 开始V2Ray节点延迟测试
2025-07-28 18:24:42 - InstagramDMTask - INFO - [模拟器7] 当前测试状态: 已连接，点击测试连接
2025-07-28 18:24:42 - InstagramDMTask - INFO - [模拟器7] 点击开始延迟测试
2025-07-28 18:24:42 - InstagramDMTask - INFO - [模拟器7] 已点击测试按钮，等待测试结果
2025-07-28 18:24:43 - InstagramDMTask - INFO - [模拟器7] 测试状态监控 (1/30): 失败：: io: read/write on closed pipe
2025-07-28 18:24:43 - InstagramDMTask - ERROR - [模拟器7] ❌ V2Ray节点延迟测试失败 (1/3): 失败：: io: read/write on closed pipe
2025-07-28 18:24:43 - InstagramDMTask - INFO - [模拟器7] 点击失败状态重置UI
2025-07-28 18:24:43 - InstagramDMTask - INFO - [模拟器7] 已点击失败状态，等待UI重置
2025-07-28 18:24:44 - InstagramDMTask - INFO - [模拟器7] 继续等待测试结果 (1/3)
2025-07-28 18:24:45 - InstagramDMTask - ERROR - [模拟器7] ❌ V2Ray节点延迟测试失败 (2/3): 失败：: io: read/write on closed pipe
2025-07-28 18:24:45 - InstagramDMTask - INFO - [模拟器7] 点击失败状态重置UI
2025-07-28 18:24:45 - InstagramDMTask - INFO - [模拟器7] 已点击失败状态，等待UI重置
2025-07-28 18:24:46 - InstagramDMTask - INFO - [模拟器7] 继续等待测试结果 (2/3)
2025-07-28 18:24:47 - InstagramDMTask - ERROR - [模拟器7] ❌ V2Ray节点延迟测试失败 (3/3): 失败：: io: read/write on closed pipe
2025-07-28 18:24:47 - InstagramDMTask - ERROR - [模拟器7] 达到最大失败次数 (3)，触发节点切换
2025-07-28 18:24:47 - InstagramDMTask - ERROR - [模拟器7] V2Ray节点延迟测试失败或超时
2025-07-28 18:24:47 - InstagramDMTask - ERROR - [模拟器7] V2Ray节点延迟测试失败，开始节点切换 (第1/8次)
2025-07-28 18:24:47 - InstagramDMTask - INFO - [模拟器7] 开始V2Ray节点切换流程
2025-07-28 18:24:47 - InstagramDMTask - INFO - [模拟器7] 开始智能滑动浏览节点列表
2025-07-28 18:24:48 - InstagramDMTask - INFO - [模拟器7] 计划执行 3 次随机滑动
2025-07-28 18:24:48 - InstagramDMTask - INFO - [模拟器7] 第 1/3 次滑动: up, 距离比例: 0.43
2025-07-28 18:24:49 - InstagramDMTask - INFO - [模拟器7] 第 1 次滑动失败
2025-07-28 18:24:49 - InstagramDMTask - INFO - [模拟器7] 滑动后等待 0.2 秒
2025-07-28 18:24:49 - InstagramDMTask - INFO - [模拟器7] 第 2/3 次滑动: up, 距离比例: 0.55
2025-07-28 18:24:49 - InstagramDMTask - INFO - [模拟器7] 第 2 次滑动失败
2025-07-28 18:24:49 - InstagramDMTask - INFO - [模拟器7] 滑动后等待 0.2 秒
2025-07-28 18:24:50 - InstagramDMTask - INFO - [模拟器7] 第 3/3 次滑动: up, 距离比例: 0.43
2025-07-28 18:24:50 - InstagramDMTask - INFO - [模拟器7] 第 3 次滑动失败
2025-07-28 18:24:50 - InstagramDMTask - INFO - [模拟器7] 滑动后等待 0.2 秒
2025-07-28 18:24:50 - InstagramDMTask - INFO - [模拟器7] ✅ 智能滑动完成，共执行 3 次
2025-07-28 18:24:50 - InstagramDMTask - INFO - [模拟器7] 开始随机选择V2Ray节点
2025-07-28 18:24:52 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 18:24:52 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 18:24:52 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 7
2025-07-28 18:24:52 - MainWindowV2 - WARNING - 模拟器7心跳状态更新未产生变化
2025-07-28 18:24:52 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 18:24:52 - InstagramDMTask - INFO - [模拟器7] 找到 7 个可用节点
2025-07-28 18:24:52 - InstagramDMTask - INFO - [模拟器7] 随机选择节点: V4-192|日本|x2.0
2025-07-28 18:24:52 - InstagramDMTask - INFO - [模拟器7] 节点位置: [11,352][88,362]
2025-07-28 18:24:52 - InstagramDMTask - INFO - [模拟器7] ✅ 成功点击节点: V4-192|日本|x2.0
2025-07-28 18:24:52 - InstagramDMTask - INFO - [模拟器7] 等待1秒让节点切换完成
2025-07-28 18:24:53 - InstagramDMTask - INFO - [模拟器7] 开始连接V2Ray节点
2025-07-28 18:24:54 - InstagramDMTask - INFO - [模拟器7] 当前连接状态: 已连接，点击测试连接
2025-07-28 18:24:54 - InstagramDMTask - INFO - [模拟器7] V2Ray节点已连接，无需重复连接
2025-07-28 18:24:54 - InstagramDMTask - INFO - [模拟器7] ✅ V2Ray节点切换成功
2025-07-28 18:24:54 - InstagramDMTask - INFO - [模拟器7] 节点切换成功，重新测试延迟
2025-07-28 18:24:54 - InstagramDMTask - INFO - [模拟器7] 开始测试V2Ray节点延迟
2025-07-28 18:24:54 - InstagramDMTask - INFO - [模拟器7] 开始V2Ray节点延迟测试
2025-07-28 18:24:55 - InstagramDMTask - INFO - [模拟器7] 当前测试状态: 已连接，点击测试连接
2025-07-28 18:24:55 - InstagramDMTask - INFO - [模拟器7] 点击开始延迟测试
2025-07-28 18:24:55 - InstagramDMTask - INFO - [模拟器7] 已点击测试按钮，等待测试结果
2025-07-28 18:24:57 - InstagramDMTask - INFO - [模拟器7] 测试状态监控 (1/30): 测试中…
2025-07-28 18:24:57 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 18:24:58 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 18:24:59 - InstagramDMTask - ERROR - [模拟器7] ❌ V2Ray节点延迟测试失败 (1/3): 失败：: context canceled
2025-07-28 18:24:59 - InstagramDMTask - INFO - [模拟器7] 点击失败状态重置UI
2025-07-28 18:25:00 - InstagramDMTask - INFO - [模拟器7] 已点击失败状态，等待UI重置
2025-07-28 18:25:00 - InstagramDMTask - INFO - [模拟器7] 继续等待测试结果 (1/3)
2025-07-28 18:25:02 - InstagramDMTask - INFO - [模拟器7] ✅ V2Ray节点延迟测试成功: 连接成功：延时 280 毫秒
2025-07-28 18:25:02 - InstagramDMTask - INFO - [模拟器7] 等待5秒后进入下一阶段
2025-07-28 18:25:07 - InstagramDMTask - INFO - [模拟器7] 开始启动Instagram应用
2025-07-28 18:25:07 - InstagramDMTask - INFO - [模拟器7] Instagram启动命令执行成功，等待应用加载
2025-07-28 18:25:10 - InstagramDMTask - INFO - [模拟器7] ✅ Instagram应用启动命令执行完成
2025-07-28 18:25:10 - InstagramDMTask - INFO - [模拟器7] Instagram启动检测 第1/5次
2025-07-28 18:25:12 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 18:25:12 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 18:25:12 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 7
2025-07-28 18:25:12 - MainWindowV2 - WARNING - 模拟器7心跳状态更新未产生变化
2025-07-28 18:25:12 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 18:25:17 - InstagramDMTask - INFO - [模拟器7] ✅ 批量验证成功
2025-07-28 18:25:17 - InstagramDMTask - INFO - [模拟器7] ✅ 验证成功
2025-07-28 18:25:17 - InstagramFollowTask - INFO - [模拟器7] Instagram页面状态检测结果: 正常-在主页面
2025-07-28 18:25:17 - InstagramFollowTask - INFO - [模拟器7] ✅ Instagram已在主页面，可以继续执行任务
2025-07-28 18:25:17 - InstagramFollowTask - INFO - [模拟器7] 开始执行关注任务，模式: direct
2025-07-28 18:25:17 - InstagramFollowTask - INFO - [模拟器7] 开始执行【模式一：直接关注模式】
2025-07-28 18:25:17 - InstagramFollowTask - INFO - [模拟器7] 开始【模式一：直接关注循环】，目标关注数: 5
2025-07-28 18:25:17 - InstagramFollowTask - INFO - [模拟器7] 开始处理目标用户: ririka_cool (已关注: 0/5)
2025-07-28 18:25:17 - InstagramFollowTask - INFO - [模拟器7] 开始处理用户：ririka_cool
2025-07-28 18:25:18 - InstagramFollowTask - INFO - [模拟器7] 跳转到用户主页：ririka_cool
2025-07-28 18:25:18 - InstagramFollowTask - INFO - [模拟器7] 开始检测用户信息
2025-07-28 18:25:25 - InstagramFollowTask - INFO - [模拟器7] 🔍 关注状态检测: 关注
2025-07-28 18:25:25 - InstagramFollowTask - INFO - [模拟器7] ✅ 标题栏验证成功，当前在用户 ririka_cool 的资料页
2025-07-28 18:25:25 - InstagramFollowTask - INFO - [模拟器7] 用户信息检测完成，耗时7.32秒
2025-07-28 18:25:25 - InstagramFollowTask - INFO - [模拟器7] 该用户未关注,开始关注
2025-07-28 18:25:28 - InstagramFollowTask - INFO - [模拟器7] 关注完成,已关注 1 / 5
2025-07-28 18:25:28 - InstagramFollowTask - INFO - [模拟器7] 直接关注任务状态更新信号已发送: 关注中 (1/5)
2025-07-28 18:25:28 - InstagramFollowTask - INFO - [模拟器7] ⏱️ 切换用户延迟: 7毫秒
2025-07-28 18:25:28 - MainWindowV2 - INFO - 模拟器7的Instagram任务状态已更新: 关注中 (1/5)
2025-07-28 18:25:28 - InstagramFollowTask - INFO - [模拟器7] 开始处理目标用户: warakano_mac (已关注: 1/5)
2025-07-28 18:25:28 - InstagramFollowTask - INFO - [模拟器7] 开始处理用户：warakano_mac
2025-07-28 18:25:28 - InstagramFollowTask - INFO - [模拟器7] 跳转到用户主页：warakano_mac
2025-07-28 18:25:28 - InstagramFollowTask - INFO - [模拟器7] 开始检测用户信息
2025-07-28 18:25:32 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 18:25:32 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 18:25:32 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 7
2025-07-28 18:25:32 - MainWindowV2 - WARNING - 模拟器7心跳状态更新未产生变化
2025-07-28 18:25:32 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 18:25:33 - InstagramFollowTask - INFO - [模拟器7] 🔍 关注状态检测: 关注
2025-07-28 18:25:33 - InstagramFollowTask - INFO - [模拟器7] ✅ 标题栏验证成功，当前在用户 warakano_mac 的资料页
2025-07-28 18:25:33 - InstagramFollowTask - INFO - [模拟器7] 用户信息检测完成，耗时4.92秒
2025-07-28 18:25:33 - InstagramFollowTask - INFO - [模拟器7] 该用户未关注,开始关注
2025-07-28 18:25:35 - InstagramFollowTask - INFO - [模拟器7] 关注完成,已关注 2 / 5
2025-07-28 18:25:35 - InstagramFollowTask - INFO - [模拟器7] 直接关注任务状态更新信号已发送: 关注中 (2/5)
2025-07-28 18:25:35 - InstagramFollowTask - INFO - [模拟器7] ⏱️ 切换用户延迟: 2毫秒
2025-07-28 18:25:35 - MainWindowV2 - INFO - 模拟器7的Instagram任务状态已更新: 关注中 (2/5)
2025-07-28 18:25:35 - InstagramFollowTask - INFO - [模拟器7] 开始处理目标用户: junpi_222 (已关注: 2/5)
2025-07-28 18:25:35 - InstagramFollowTask - INFO - [模拟器7] 开始处理用户：junpi_222
2025-07-28 18:25:36 - InstagramFollowTask - INFO - [模拟器7] 跳转到用户主页：junpi_222
2025-07-28 18:25:36 - InstagramFollowTask - INFO - [模拟器7] 开始检测用户信息
2025-07-28 18:25:41 - InstagramFollowTask - INFO - [模拟器7] 🔍 关注状态检测: 关注
2025-07-28 18:25:41 - InstagramFollowTask - INFO - [模拟器7] ✅ 标题栏验证成功，当前在用户 junpi_222 的资料页
2025-07-28 18:25:41 - InstagramFollowTask - INFO - [模拟器7] 用户信息检测完成，耗时5.75秒
2025-07-28 18:25:41 - InstagramFollowTask - INFO - [模拟器7] 该用户未关注,开始关注
2025-07-28 18:25:44 - InstagramFollowTask - INFO - [模拟器7] 检测到Instagram关注配置变化: instagram_follow.direct_follow_count = 5 → 3
2025-07-28 18:25:44 - InstagramFollowTask - INFO - [模拟器7] Instagram关注任务配置加载完成
2025-07-28 18:25:44 - InstagramFollowTask - INFO - [模拟器7] Instagram关注任务配置已热加载更新
2025-07-28 18:25:45 - InstagramFollowTask - INFO - [模拟器7] 关注完成,已关注 3 / 3
2025-07-28 18:25:45 - InstagramFollowTask - INFO - [模拟器7] 直接关注任务状态更新信号已发送: 已完成 (3/3)
2025-07-28 18:25:45 - MainWindowV2 - INFO - 模拟器7的Instagram任务状态已更新: 已完成 (3/3)
2025-07-28 18:25:45 - InstagramFollowTask - INFO - [模拟器7] 任务完成,退出循环.--任务进度 :3 / 3 耗时: 27.89秒
2025-07-28 18:25:45 - InstagramFollowTask - INFO - [模拟器7] 【模式一：直接关注循环】完成，成功关注: 3, 跳过蓝V: 0, 跳过私密: 0
2025-07-28 18:25:45 - InstagramDMTask - INFO - [模拟器7] Instagram任务配置观察者已注销
2025-07-28 18:25:45 - InstagramDMTask - INFO - [模拟器7] 开始任务完成后清理工作
2025-07-28 18:25:45 - TaskActivityHeartbeatManager - INFO - 模拟器 7 已从任务活动监控移除
2025-07-28 18:25:45 - InstagramDMTask - INFO - [模拟器7] 心跳监控已移除
2025-07-28 18:25:45 - InstagramDMTask - INFO - [模拟器7] 准备关闭模拟器
2025-07-28 18:25:45 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 7 | remaining_running: 0
2025-07-28 18:25:45 - Emulator - INFO - 模拟器停止成功 | emulator_id: 7
2025-07-28 18:25:45 - InstagramDMTask - INFO - [模拟器7] 模拟器已成功关闭
2025-07-28 18:25:45 - MainWindowV2 - INFO - 任务完成: 模拟器7, 任务stop
2025-07-28 18:25:45 - InstagramDMTask - INFO - [模拟器7] 任务完成后清理工作完成
2025-07-28 18:25:45 - MainWindowV2 - INFO - 模拟器7停止成功
2025-07-28 18:25:45 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=7, 任务类型=stop, 结果={'result': '停止成功', 'message': ''}
2025-07-28 18:25:45 - InstagramFollowTaskThread - INFO - [模拟器7] Instagram直接关注任务执行成功
2025-07-28 18:25:45 - WindowArrangementManager - INFO - 跳过非启动任务或失败任务: 任务类型=stop, 结果=停止成功
2025-07-28 18:25:56 - FixedAsyncBridge - INFO - 执行异步操作: instagram_follow_direct_task
2025-07-28 18:25:56 - MainWindowV2 - INFO - Instagram直接关注任务已启动，涉及1个模拟器
2025-07-28 18:25:56 - FixedAsyncBridge - INFO - 异步桥接器: 处理Instagram直接关注任务请求
2025-07-28 18:25:56 - MainWindowV2 - INFO - 用户启动Instagram直接关注任务，模拟器数量: 1
2025-07-28 18:25:56 - FixedAsyncBridge - INFO - 开始处理Instagram直接关注任务（线程池模式），模拟器数量: 1
2025-07-28 18:25:56 - FixedAsyncBridge - INFO - 使用批量启动功能启动模拟器: [5]
2025-07-28 18:25:56 - FixedAsyncBridge - INFO - 异步桥接器: 处理批量启动请求，模拟器数量: 1
2025-07-28 18:25:56 - StartupManager - INFO - 批量启动请求 | count: 1
2025-07-28 18:25:56 - StartupManager - INFO - 启动调度器已启动
2025-07-28 18:25:56 - MainWindowV2 - INFO - 模拟器5: 未知 -> 排队中
2025-07-28 18:25:56 - FixedAsyncBridge - INFO - 异步桥接器: 批量启动请求已处理，状态: queued
2025-07-28 18:25:56 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 1 | queued: 1 | starting: 0 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 0 | max_concurrent: 1
2025-07-28 18:25:56 - FixedAsyncBridge - INFO - 模拟器批量启动成功，开始创建关注任务线程池
2025-07-28 18:25:56 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中0个, 运行0个, 失败0个 (并发槽位:0/1)
2025-07-28 18:25:56 - InstagramTaskManager - INFO - Instagram任务管理器已连接模拟器状态变化信号
2025-07-28 18:25:56 - InstagramTaskManager - INFO - Instagram任务管理器初始化完成，最大并发: 1
2025-07-28 18:25:56 - FixedAsyncBridge - INFO - 异步操作完成: start_batch
2025-07-28 18:25:56 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器初始化完成，模式: direct
2025-07-28 18:25:56 - MainWindowV2 - INFO - 批量启动完成: 0/1 成功
2025-07-28 18:25:56 - InstagramTaskManager - INFO - 开始启动Instagram任务管理器，模拟器数量: 1
2025-07-28 18:25:56 - MainWindowV2 - INFO - 批量启动操作完成 | success: 0 | total: 1
2025-07-28 18:25:56 - InstagramFollowTaskThread - INFO - [模拟器5] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 18:25:56 - InstagramFollowTaskManager - INFO - 为模拟器 5 创建Instagram关注任务线程，模式: direct
2025-07-28 18:25:56 - InstagramTaskManager - INFO - 线程池创建完成: 成功创建1个Instagram线程
2025-07-28 18:25:56 - FixedAsyncBridge - INFO - 异步桥接器: Instagram直接关注任务请求已处理，状态: started
2025-07-28 18:25:56 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-28 18:25:56 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-28 18:25:56 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-28 18:25:56 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-28 18:25:56 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-28 18:25:56 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 5
2025-07-28 18:25:56 - FixedAsyncBridge - INFO - 异步操作完成: instagram_follow_direct_task
2025-07-28 18:25:56 - Emulator - INFO - 模拟器状态变化 | emulator_id: 5 | old_state: 排队中 | new_state: 启动中
2025-07-28 18:25:56 - MainWindowV2 - INFO - 模拟器5: 排队中 -> 启动中
2025-07-28 18:25:56 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 1 | queued: 0 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-28 18:25:56 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/1)
2025-07-28 18:25:56 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 5
2025-07-28 18:25:57 - Emulator - INFO - Android系统启动完成 | emulator_id: 5 | elapsed_time: 0.2秒
2025-07-28 18:25:57 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器5状态变化: 启动中 -> 运行中
2025-07-28 18:25:57 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 启动中 -> 运行中
2025-07-28 18:25:57 - Emulator - INFO - 模拟器状态变化 | emulator_id: 5 | old_state: 启动中 | new_state: 运行中
2025-07-28 18:25:57 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 启动中 -> 运行中
2025-07-28 18:25:57 - TaskActivityHeartbeatManager - INFO - 模拟器 5 已添加到任务活动监控，失败计数: 0
2025-07-28 18:25:57 - InstagramFollowTaskManager - INFO - 启动模拟器5的Instagram关注任务线程 - 当前并发: 1/1
2025-07-28 18:25:57 - Emulator - INFO - 模拟器启动成功 | emulator_id: 5 | running_count: 1
2025-07-28 18:25:57 - MainWindowV2 - INFO - 任务完成: 模拟器5, 任务start
2025-07-28 18:25:57 - MainWindowV2 - INFO - 模拟器5启动成功
2025-07-28 18:25:57 - InstagramTaskThread - INFO - [模拟器5] 开始等待启动完成
2025-07-28 18:25:57 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=5, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 18:25:57 - InstagramTaskThread - INFO - [模拟器5] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 18:25:57 - InstagramTaskThread - INFO - [模拟器5] 开始窗口排列
2025-07-28 18:25:57 - WindowArrangementManager - INFO - 模拟器5启动完成，立即触发窗口排列
2025-07-28 18:25:57 - MainWindowV2 - WARNING - 未找到模拟器5，无法更新状态
2025-07-28 18:25:57 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中
2025-07-28 18:25:57 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 1 | queued: 0 | starting: 0 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 100.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-28 18:25:57 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中0个, 运行1个, 失败0个 (并发槽位:1/1)
2025-07-28 18:25:57 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中, PID: 22944
2025-07-28 18:25:57 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中
2025-07-28 18:25:57 - StartupManager - INFO - 调度器已停止
2025-07-28 18:25:58 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 18:25:58 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 18:25:59 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-28 18:25:59 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-28 18:25:59 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 18:25:59 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 18:25:59 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-5' 已在网格位置 (0, 1)，保持不动
2025-07-28 18:25:59 - WindowArrangementManager - INFO - 所有窗口都已在网格位置，无需排列
2025-07-28 18:25:59 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-28 18:25:59 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-28 18:26:05 - InstagramTaskThread - INFO - [模拟器5] 窗口排列完成
2025-07-28 18:26:05 - InstagramFollowTaskThread - INFO - [模拟器5] 开始执行Instagram直接关注任务
2025-07-28 18:26:05 - InstagramDMTask - INFO - [模拟器5] 雷电模拟器API初始化成功
2025-07-28 18:26:05 - InstagramDMTask - INFO - [模拟器5] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 18:26:05 - InstagramDMTask - INFO - [模拟器5] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 18:26:05 - InstagramDMTask - INFO - [模拟器5] 已设置ld.emulator_id = 5
2025-07-28 18:26:05 - InstagramDMTask - INFO - [模拟器5] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 18:26:05 - InstagramDMTask - INFO - [模拟器5] Instagram任务配置热加载观察者已注册
2025-07-28 18:26:05 - InstagramDMTask - INFO - [模拟器5] Instagram私信任务执行器初始化完成
2025-07-28 18:26:05 - InstagramFollowTask - INFO - [模拟器5] Instagram关注任务配置加载完成
2025-07-28 18:26:05 - InstagramFollowTask - INFO - [模拟器5] Instagram关注任务执行器初始化完成
2025-07-28 18:26:05 - InstagramFollowTask - INFO - [模拟器5] 关注模式已设置为: direct
2025-07-28 18:26:05 - InstagramFollowTask - INFO - [模拟器5] 开始执行Instagram关注任务
2025-07-28 18:26:05 - InstagramFollowTask - WARNING - [模拟器5] 任务开始时间未由线程传递，在此设置
2025-07-28 18:26:05 - InstagramFollowTask - INFO - [模拟器5] 任务超时设置: 666秒，已运行: 0.00秒
2025-07-28 18:26:05 - InstagramDMTask - INFO - [模拟器5] 模拟器Android系统运行正常，桌面稳定
2025-07-28 18:26:05 - InstagramDMTask - INFO - [模拟器5] 开始检查应用安装状态
2025-07-28 18:26:05 - InstagramDMTask - INFO - [模拟器5] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 18:26:05 - InstagramDMTask - INFO - [模拟器5] ✅ Instagram已安装，版本: 278.0.0.21.117
2025-07-28 18:26:05 - InstagramDMTask - INFO - [模拟器5] 📊 应用安装状态检测结果:
2025-07-28 18:26:05 - InstagramDMTask - INFO - [模拟器5] ✅ 所有必要应用已安装
2025-07-28 18:26:05 - InstagramDMTask - INFO - [模拟器5] 开始启动V2Ray应用
2025-07-28 18:26:05 - InstagramDMTask - INFO - [模拟器5] V2Ray启动命令执行成功，等待应用加载
2025-07-28 18:26:05 - InstagramDMTask - INFO - [模拟器5] V2Ray应用启动结果: 成功
2025-07-28 18:26:08 - InstagramDMTask - INFO - [模拟器5] ✅ V2Ray应用启动成功
2025-07-28 18:26:08 - InstagramDMTask - INFO - [模拟器5] 开始检查V2Ray节点列表状态
2025-07-28 18:26:09 - InstagramDMTask - INFO - [模拟器5] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 18:26:09 - InstagramDMTask - INFO - [模拟器5] 开始连接V2Ray节点
2025-07-28 18:26:11 - InstagramDMTask - INFO - [模拟器5] 当前连接状态: 连接成功：延时 237 毫秒
2025-07-28 18:26:11 - InstagramDMTask - ERROR - [模拟器5] 未知的连接状态: 连接成功：延时 237 毫秒
2025-07-28 18:26:11 - InstagramFollowTaskThread - ERROR - [模拟器5] Instagram直接关注任务执行失败: V2Ray节点连接失败
2025-07-28 18:26:12 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 18:26:12 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 18:26:12 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 5
2025-07-28 18:26:12 - MainWindowV2 - WARNING - 模拟器5心跳状态更新未产生变化
2025-07-28 18:26:12 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 18:26:32 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 18:26:32 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 18:26:32 - TaskActivityHeartbeatManager - INFO - 模拟器 5 疑似心跳异常，进入观察期 | 无活动时间: 21.0秒
2025-07-28 18:26:32 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 18:26:52 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 18:26:52 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 18:26:52 - TaskActivityHeartbeatManager - ERROR - 模拟器 5 确认心跳异常 | 总无活动时间: 41.0秒
2025-07-28 18:26:52 - TaskActivityHeartbeatManager - INFO - 心跳异常处理开始，失败次数: 1/1 | emulator_id: 5
2025-07-28 18:26:52 - LeiDianNativeAPI - INFO - 雷电原生API初始化成功: G:/leidian/LDPlayer9
2025-07-28 18:26:52 - NativeScreenshotEngine - INFO - 专业截图引擎初始化成功
2025-07-28 18:26:52 - ScreenshotManager - INFO - 原生截图引擎初始化成功
2025-07-28 18:26:52 - UnifiedEmulatorManager - INFO - 心跳检测失败: 心跳异常, 失败次数: 1 | emulator_id: 5
2025-07-28 18:26:52 - NativeScreenshotEngine - INFO - 开始截取模拟器 5 的截图
2025-07-28 18:26:52 - MainWindowV2 - INFO - 模拟器5心跳异常: 心跳异常 (失败次数: 1)
2025-07-28 18:26:52 - MainWindowV2 - WARNING - 模拟器5心跳检测失败: 心跳异常, 失败次数: 1
2025-07-28 18:26:52 - MainWindowV2 - WARNING - 模拟器5心跳状态更新未产生变化
2025-07-28 18:26:52 - NativeScreenshotEngine - INFO - 模拟器 5 截图成功: test_screenshots\emulator_5_心跳异常_第1次失败_1_20250728_182652.png
2025-07-28 18:26:52 - ScreenshotManager - INFO - 异常截图已保存: test_screenshots\emulator_5_心跳异常_第1次失败_1_20250728_182652.png
2025-07-28 18:26:52 - TaskActivityHeartbeatManager - INFO - 开始熔断处理 | emulator_id: 5
2025-07-28 18:26:52 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 5 | remaining_running: 0
2025-07-28 18:26:52 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器5状态变化: 运行中 -> 异常
2025-07-28 18:26:52 - InstagramTaskManager - INFO - 模拟器5没有需要清理的Instagram线程
2025-07-28 18:26:52 - Emulator - INFO - 模拟器状态变化 | emulator_id: 5 | old_state: 运行中 | new_state: 异常
2025-07-28 18:26:52 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 运行中 -> 异常
2025-07-28 18:26:52 - MainWindowV2 - INFO - 模拟器5: 运行中 -> 异常
2025-07-28 18:26:52 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器5状态变化: 运行中 -> 异常
2025-07-28 18:26:52 - InstagramTaskManager - INFO - 模拟器5没有需要清理的Instagram线程
2025-07-28 18:26:52 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 运行中 -> 异常
2025-07-28 18:26:52 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器5状态变化: 运行中 -> 异常
2025-07-28 18:26:52 - InstagramTaskManager - INFO - 清理模拟器5的运行中Instagram线程，原因: 模拟器状态变为异常
2025-07-28 18:26:52 - InstagramTaskManager - INFO - 模拟器5的Instagram线程已清理完成，当前并发: 0/1
2025-07-28 18:26:52 - TaskActivityHeartbeatManager - INFO - 模拟器已标记为异常并清理状态 | emulator_id: 5 | removed_from_running: True | removed_from_active: False | total_running: 0 | total_active: 0
2025-07-28 18:26:52 - TaskActivityHeartbeatManager - INFO - 模拟器 5 已从任务活动监控移除
2025-07-28 18:26:52 - TaskActivityHeartbeatManager - INFO - 没有排队等待接力的模拟器
2025-07-28 18:26:52 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 1 | queued: 0 | starting: 0 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 0 | max_concurrent: 1
2025-07-28 18:26:52 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中0个, 运行0个, 失败0个 (并发槽位:0/1)
2025-07-28 18:26:52 - UnifiedEmulatorManager - INFO - 模拟器已切换: 5 -> -1
2025-07-28 18:26:52 - MainWindowV2 - INFO - 模拟器自动切换: 5 -> -1
2025-07-28 18:26:52 - MainWindowV2 - INFO - 模拟器已自动切换: 5 -> -1
2025-07-28 18:26:52 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 18:27:02 - FixedAsyncBridge - INFO - 执行异步操作: instagram_follow_direct_task
2025-07-28 18:27:02 - MainWindowV2 - INFO - Instagram直接关注任务已启动，涉及1个模拟器
2025-07-28 18:27:02 - FixedAsyncBridge - INFO - 异步桥接器: 处理Instagram直接关注任务请求
2025-07-28 18:27:02 - MainWindowV2 - INFO - 用户启动Instagram直接关注任务，模拟器数量: 1
2025-07-28 18:27:02 - FixedAsyncBridge - INFO - 开始处理Instagram直接关注任务（线程池模式），模拟器数量: 1
2025-07-28 18:27:02 - FixedAsyncBridge - INFO - 使用批量启动功能启动模拟器: [5]
2025-07-28 18:27:02 - FixedAsyncBridge - INFO - 异步桥接器: 处理批量启动请求，模拟器数量: 1
2025-07-28 18:27:02 - StartupManager - INFO - 批量启动请求 | count: 1
2025-07-28 18:27:02 - StartupManager - INFO - 启动调度器已启动
2025-07-28 18:27:02 - FixedAsyncBridge - INFO - 异步桥接器: 批量启动请求已处理，状态: queued
2025-07-28 18:27:02 - MainWindowV2 - INFO - 模拟器5: 未知 -> 排队中
2025-07-28 18:27:02 - FixedAsyncBridge - INFO - 模拟器批量启动成功，开始创建关注任务线程池
2025-07-28 18:27:02 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 1 | queued: 1 | starting: 0 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 0 | max_concurrent: 1
2025-07-28 18:27:02 - InstagramTaskManager - INFO - Instagram任务管理器已连接模拟器状态变化信号
2025-07-28 18:27:02 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中0个, 运行0个, 失败0个 (并发槽位:0/1)
2025-07-28 18:27:02 - InstagramTaskManager - INFO - Instagram任务管理器初始化完成，最大并发: 1
2025-07-28 18:27:02 - FixedAsyncBridge - INFO - 异步操作完成: start_batch
2025-07-28 18:27:02 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器初始化完成，模式: direct
2025-07-28 18:27:02 - MainWindowV2 - INFO - 批量启动完成: 0/1 成功
2025-07-28 18:27:02 - MainWindowV2 - INFO - 批量启动操作完成 | success: 0 | total: 1
2025-07-28 18:27:02 - InstagramTaskManager - INFO - 开始启动Instagram任务管理器，模拟器数量: 1
2025-07-28 18:27:02 - InstagramFollowTaskThread - INFO - [模拟器5] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 18:27:02 - InstagramFollowTaskManager - INFO - 为模拟器 5 创建Instagram关注任务线程，模式: direct
2025-07-28 18:27:02 - InstagramTaskManager - INFO - 线程池创建完成: 成功创建1个Instagram线程
2025-07-28 18:27:02 - FixedAsyncBridge - INFO - 异步桥接器: Instagram直接关注任务请求已处理，状态: started
2025-07-28 18:27:02 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-28 18:27:02 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-28 18:27:02 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-28 18:27:02 - FixedAsyncBridge - INFO - 异步操作完成: instagram_follow_direct_task
2025-07-28 18:27:02 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-28 18:27:02 - Emulator - INFO - 模拟器状态变化 | emulator_id: 5 | old_state: 排队中 | new_state: 启动中
2025-07-28 18:27:02 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-28 18:27:02 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-28 18:27:02 - MainWindowV2 - INFO - 模拟器5: 排队中 -> 启动中
2025-07-28 18:27:02 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-28 18:27:02 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 5
2025-07-28 18:27:02 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 1 | queued: 0 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-28 18:27:02 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/1)
2025-07-28 18:27:03 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 5
2025-07-28 18:27:12 - Emulator - INFO - Android系统启动完成 | emulator_id: 5 | elapsed_time: 9.9秒
2025-07-28 18:27:12 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器5状态变化: 启动中 -> 运行中
2025-07-28 18:27:12 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 启动中 -> 运行中
2025-07-28 18:27:12 - Emulator - INFO - 模拟器状态变化 | emulator_id: 5 | old_state: 启动中 | new_state: 运行中
2025-07-28 18:27:12 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 启动中 -> 运行中
2025-07-28 18:27:12 - TaskActivityHeartbeatManager - INFO - 模拟器 5 已添加到任务活动监控，失败计数: 0
2025-07-28 18:27:12 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 启动中 -> 运行中
2025-07-28 18:27:12 - InstagramFollowTaskManager - INFO - 启动模拟器5的Instagram关注任务线程 - 当前并发: 1/1
2025-07-28 18:27:12 - MainWindowV2 - INFO - 任务完成: 模拟器5, 任务start
2025-07-28 18:27:12 - Emulator - INFO - 模拟器启动成功 | emulator_id: 5 | running_count: 1
2025-07-28 18:27:12 - MainWindowV2 - INFO - 模拟器5启动成功
2025-07-28 18:27:12 - InstagramTaskThread - INFO - [模拟器5] 开始等待启动完成
2025-07-28 18:27:12 - InstagramTaskThread - INFO - [模拟器5] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 18:27:12 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=5, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 18:27:12 - InstagramTaskThread - INFO - [模拟器5] 开始窗口排列
2025-07-28 18:27:12 - WindowArrangementManager - INFO - 模拟器5启动完成，立即触发窗口排列
2025-07-28 18:27:12 - MainWindowV2 - WARNING - 未找到模拟器5，无法更新状态
2025-07-28 18:27:12 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中
2025-07-28 18:27:12 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 1 | queued: 0 | starting: 0 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 100.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-28 18:27:12 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中0个, 运行1个, 失败0个 (并发槽位:1/1)
2025-07-28 18:27:13 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中, PID: 26336
2025-07-28 18:27:13 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中
