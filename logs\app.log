2025-07-29 08:23:59 - root - INFO - 简化日志系统初始化完成
2025-07-29 08:23:59 - main - INFO - 应用程序启动
2025-07-29 08:23:59 - __main__ - INFO - Qt应用程序已创建
2025-07-29 08:23:59 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-29 08:23:59 - __main__ - INFO - 统一配置管理器已创建
2025-07-29 08:23:59 - DatabaseManager - INFO - SQLite连接已配置WAL模式
2025-07-29 08:23:59 - DatabaseManager - ERROR - 检查表结构失败: 1
2025-07-29 08:23:59 - DatabaseManager - INFO - 数据库表结构创建完成
2025-07-29 08:23:59 - DatabaseManager - INFO - 数据库初始化完成: E:\VScodexiangmu\insleidian2\emulator_system.db
2025-07-29 08:23:59 - EmulatorRepository - INFO - 模拟器数据仓库初始化完成
2025-07-29 08:23:59 - StartupManager - INFO - 统一启动管理器初始化完成
2025-07-29 08:23:59 - TaskActivityHeartbeatManager - INFO - 心跳检测已启用
2025-07-29 08:23:59 - TaskActivityHeartbeatManager - INFO - 任务活动检测心跳管理器初始化完成
2025-07-29 08:23:59 - TaskActivityHeartbeatManager - INFO - 心跳监控服务已启动，检测间隔: 20秒
2025-07-29 08:23:59 - UnifiedEmulatorManager - INFO - 统一模拟器管理器初始化完成
2025-07-29 08:23:59 - __main__ - INFO - 修复版异步桥梁已创建
2025-07-29 08:23:59 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-29 08:23:59 - BasicConfigUI - INFO - 基础配置信号连接完成
2025-07-29 08:23:59 - BasicConfigUI - INFO - 基础配置UI已注册为配置观察者，支持热加载
2025-07-29 08:23:59 - BasicConfigUI - INFO - 浏览按钮事件连接完成
2025-07-29 08:23:59 - BasicConfigUI - INFO - 基础配置UI初始化完成
2025-07-29 08:23:59 - InstagramDMUI - INFO - Instagram私信配置加载完成
2025-07-29 08:23:59 - InstagramFollowUI - INFO - Instagram关注配置加载完成
2025-07-29 08:23:59 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 程序启动)
2025-07-29 08:23:59 - MainWindowV2 - INFO - 事件驱动日志更新已启动 - 表格运行日志列实时更新（替代定时器）
2025-07-29 08:23:59 - MainWindowV2 - INFO - Instagram任务状态更新信号已连接
2025-07-29 08:23:59 - __main__ - INFO - UI主窗口已创建
2025-07-29 08:23:59 - MainWindowV2 - INFO - UI主窗口已创建
2025-07-29 08:24:00 - __main__ - INFO - 主窗口已显示
2025-07-29 08:24:00 - WindowArrangementManager - INFO - 窗口排列管理器初始化完成
2025-07-29 08:24:00 - FixedAsyncBridge - INFO - 任务完成信号已连接到窗口排列管理器
2025-07-29 08:24:00 - FixedAsyncBridge - INFO - 任务完成信号、状态变化信号、心跳监控信号和窗口排列信号已连接到UI
2025-07-29 08:24:00 - FixedAsyncBridge - INFO - 采用事件驱动方式，无需监控器
2025-07-29 08:24:00 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-29 08:24:00 - AsyncEventLoopManager - INFO - 专用异步事件循环已启动
2025-07-29 08:24:00 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-29 08:24:00 - __main__ - INFO - UI层和业务层已连接
2025-07-29 08:24:00 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-29 08:24:00 - __main__ - INFO - 启动Qt事件循环
2025-07-29 08:24:00 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-29 08:24:00 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-29 08:24:00 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 路径变化)
2025-07-29 08:24:00 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-29 08:24:00 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-29 08:24:00 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-29 08:24:00 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-29 08:24:00 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-29 08:24:00 - App - INFO - ldconsole命令执行成功，输出长度: 48410
2025-07-29 08:24:00 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-29 08:24:00 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-29 08:24:00 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-29 08:24:00 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-29 08:24:00 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.27s | count: 1229
2025-07-29 08:24:00 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-29 08:24:00 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-29 08:24:00 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-29 08:24:00 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-29 08:24:00 - App - INFO - ldconsole命令执行成功，输出长度: 48410
2025-07-29 08:24:00 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-29 08:24:00 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-29 08:24:00 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-29 08:24:00 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-29 08:24:00 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.44s | count: 1229
2025-07-29 08:24:00 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-29 08:24:00 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-29 08:24:00 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-29 08:24:00 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-29 08:24:01 - ConfigHotReloadService - INFO - 配置热加载服务初始化完成（Qt定时器模式）
2025-07-29 08:24:01 - ConfigHotReloadService - INFO - 配置热加载服务已启动（Qt定时器模式）
2025-07-29 08:24:01 - FixedAsyncBridge - INFO - 后台服务已启动
2025-07-29 08:24:01 - __main__ - INFO - 后台服务已启动
2025-07-29 08:24:01 - __main__ - INFO - 延迟启动服务完成
