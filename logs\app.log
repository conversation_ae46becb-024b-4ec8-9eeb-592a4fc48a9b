2025-07-29 09:35:09 - root - INFO - 简化日志系统初始化完成
2025-07-29 09:35:09 - main - INFO - 应用程序启动
2025-07-29 09:35:09 - __main__ - INFO - Qt应用程序已创建
2025-07-29 09:35:09 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-29 09:35:09 - __main__ - INFO - 统一配置管理器已创建
2025-07-29 09:35:09 - DatabaseManager - INFO - SQLite连接已配置WAL模式
2025-07-29 09:35:09 - DatabaseManager - ERROR - 检查表结构失败: 1
2025-07-29 09:35:09 - DatabaseManager - INFO - 数据库表结构创建完成
2025-07-29 09:35:09 - DatabaseManager - INFO - 数据库初始化完成: E:\VScodexiangmu\insleidian2\emulator_system.db
2025-07-29 09:35:09 - EmulatorRepository - INFO - 模拟器数据仓库初始化完成
2025-07-29 09:35:09 - StartupManager - INFO - 统一启动管理器初始化完成
2025-07-29 09:35:09 - TaskActivityHeartbeatManager - INFO - 心跳检测已启用
2025-07-29 09:35:09 - TaskActivityHeartbeatManager - INFO - 任务活动检测心跳管理器初始化完成
2025-07-29 09:35:09 - TaskActivityHeartbeatManager - INFO - 心跳监控服务已启动，检测间隔: 20秒
2025-07-29 09:35:09 - UnifiedEmulatorManager - INFO - 统一模拟器管理器初始化完成
2025-07-29 09:35:09 - __main__ - INFO - 修复版异步桥梁已创建
2025-07-29 09:35:09 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-29 09:35:09 - BasicConfigUI - INFO - 基础配置信号连接完成
2025-07-29 09:35:09 - BasicConfigUI - INFO - 基础配置UI已注册为配置观察者，支持热加载
2025-07-29 09:35:09 - BasicConfigUI - INFO - 浏览按钮事件连接完成
2025-07-29 09:35:09 - BasicConfigUI - INFO - 基础配置UI初始化完成
2025-07-29 09:35:09 - InstagramSummaryService - INFO - Instagram汇总统计服务已初始化
2025-07-29 09:35:09 - InstagramDMUI - INFO - Instagram私信配置加载完成
2025-07-29 09:35:09 - InstagramSummaryService - INFO - Instagram汇总统计服务已初始化
2025-07-29 09:35:09 - InstagramFollowUI - INFO - Instagram关注配置加载完成
2025-07-29 09:35:09 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 程序启动)
2025-07-29 09:35:09 - MainWindowV2 - INFO - 事件驱动日志更新已启动 - 表格运行日志列实时更新（替代定时器）
2025-07-29 09:35:09 - MainWindowV2 - INFO - Instagram任务状态更新信号已连接
2025-07-29 09:35:09 - __main__ - INFO - UI主窗口已创建
2025-07-29 09:35:09 - MainWindowV2 - INFO - UI主窗口已创建
2025-07-29 09:35:10 - __main__ - INFO - 主窗口已显示
2025-07-29 09:35:10 - WindowArrangementManager - INFO - 窗口排列管理器初始化完成
2025-07-29 09:35:10 - FixedAsyncBridge - INFO - 任务完成信号已连接到窗口排列管理器
2025-07-29 09:35:10 - FixedAsyncBridge - INFO - 任务完成信号、状态变化信号、心跳监控信号和窗口排列信号已连接到UI
2025-07-29 09:35:10 - FixedAsyncBridge - INFO - 采用事件驱动方式，无需监控器
2025-07-29 09:35:10 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-29 09:35:10 - AsyncEventLoopManager - INFO - 专用异步事件循环已启动
2025-07-29 09:35:10 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-29 09:35:10 - __main__ - INFO - UI层和业务层已连接
2025-07-29 09:35:10 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-29 09:35:10 - __main__ - INFO - 启动Qt事件循环
2025-07-29 09:35:10 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-29 09:35:10 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-29 09:35:10 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 路径变化)
2025-07-29 09:35:10 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-29 09:35:10 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-29 09:35:10 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-29 09:35:10 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-29 09:35:10 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-29 09:35:10 - App - INFO - ldconsole命令执行成功，输出长度: 48413
2025-07-29 09:35:10 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-29 09:35:10 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-29 09:35:10 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-29 09:35:10 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-29 09:35:10 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.36s | count: 1229
2025-07-29 09:35:10 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-29 09:35:10 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-29 09:35:10 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-29 09:35:10 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-29 09:35:10 - App - INFO - ldconsole命令执行成功，输出长度: 48413
2025-07-29 09:35:10 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-29 09:35:10 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-29 09:35:10 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-29 09:35:10 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-29 09:35:10 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.27s | count: 1229
2025-07-29 09:35:10 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-29 09:35:10 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-29 09:35:10 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-29 09:35:10 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-29 09:35:11 - ConfigHotReloadService - INFO - 配置热加载服务初始化完成（Qt定时器模式）
2025-07-29 09:35:11 - ConfigHotReloadService - INFO - 配置热加载服务已启动（Qt定时器模式）
2025-07-29 09:35:11 - FixedAsyncBridge - INFO - 后台服务已启动
2025-07-29 09:35:11 - __main__ - INFO - 后台服务已启动
2025-07-29 09:35:11 - __main__ - INFO - 延迟启动服务完成
2025-07-29 09:35:13 - FixedAsyncBridge - INFO - 执行异步操作: instagram_follow_direct_task
2025-07-29 09:35:13 - MainWindowV2 - INFO - Instagram直接关注任务已启动，涉及2个模拟器
2025-07-29 09:35:13 - FixedAsyncBridge - INFO - 异步桥接器: 处理Instagram直接关注任务请求
2025-07-29 09:35:13 - MainWindowV2 - INFO - 用户启动Instagram直接关注任务，模拟器数量: 2
2025-07-29 09:35:13 - FixedAsyncBridge - INFO - 开始处理Instagram直接关注任务（线程池模式），模拟器数量: 2
2025-07-29 09:35:13 - FixedAsyncBridge - INFO - 使用批量启动功能启动模拟器: [6, 7]
2025-07-29 09:35:13 - FixedAsyncBridge - INFO - 异步桥接器: 处理批量启动请求，模拟器数量: 2
2025-07-29 09:35:13 - StartupManager - INFO - 批量启动请求 | count: 2
2025-07-29 09:35:13 - StartupManager - INFO - 启动调度器已启动
2025-07-29 09:35:13 - FixedAsyncBridge - INFO - 异步桥接器: 批量启动请求已处理，状态: queued
2025-07-29 09:35:13 - FixedAsyncBridge - INFO - 模拟器批量启动成功，开始创建关注任务线程池
2025-07-29 09:35:13 - InstagramTaskManager - INFO - Instagram任务管理器已连接模拟器状态变化信号
2025-07-29 09:35:13 - InstagramTaskManager - INFO - Instagram任务管理器初始化完成，最大并发: 1
2025-07-29 09:35:13 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器初始化完成，模式: direct
2025-07-29 09:35:13 - InstagramTaskManager - INFO - 开始启动Instagram任务管理器，模拟器数量: 2
2025-07-29 09:35:13 - InstagramFollowTaskThread - INFO - [模拟器6] Instagram关注任务线程初始化完成，模式: direct
2025-07-29 09:35:13 - InstagramFollowTaskManager - INFO - 为模拟器 6 创建Instagram关注任务线程，模式: direct
2025-07-29 09:35:13 - InstagramFollowTaskThread - INFO - [模拟器7] Instagram关注任务线程初始化完成，模式: direct
2025-07-29 09:35:13 - InstagramFollowTaskManager - INFO - 为模拟器 7 创建Instagram关注任务线程，模式: direct
2025-07-29 09:35:13 - InstagramTaskManager - INFO - 线程池创建完成: 成功创建2个Instagram线程
2025-07-29 09:35:13 - FixedAsyncBridge - INFO - 异步桥接器: Instagram直接关注任务请求已处理，状态: started
2025-07-29 09:35:13 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器6状态变化: 排队中 -> 启动中
2025-07-29 09:35:13 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器6状态变化: 排队中 -> 启动中
2025-07-29 09:35:13 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 6
2025-07-29 09:35:13 - MainWindowV2 - INFO - 2个模拟器: 未知 -> 排队中
2025-07-29 09:35:13 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 2 | queued: 2 | starting: 0 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 0 | max_concurrent: 1
2025-07-29 09:35:13 - MainWindowV2 - INFO - 启动进度: 排队2个, 启动中0个, 运行0个, 失败0个 (并发槽位:0/1)
2025-07-29 09:35:13 - FixedAsyncBridge - INFO - 异步操作完成: start_batch
2025-07-29 09:35:13 - MainWindowV2 - INFO - 批量启动完成: 0/2 成功
2025-07-29 09:35:13 - MainWindowV2 - INFO - 批量启动操作完成 | success: 0 | total: 2
2025-07-29 09:35:13 - FixedAsyncBridge - INFO - 异步操作完成: instagram_follow_direct_task
2025-07-29 09:35:13 - Emulator - INFO - 模拟器状态变化 | emulator_id: 6 | old_state: 排队中 | new_state: 启动中
2025-07-29 09:35:13 - MainWindowV2 - INFO - 模拟器6: 排队中 -> 启动中
2025-07-29 09:35:13 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 2 | queued: 1 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-29 09:35:13 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/1)
2025-07-29 09:35:13 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 6
2025-07-29 09:35:23 - Emulator - INFO - Android系统启动完成 | emulator_id: 6 | elapsed_time: 9.9秒
2025-07-29 09:35:23 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器6状态变化: 启动中 -> 运行中
2025-07-29 09:35:23 - InstagramFollowTaskManager - INFO - 启动模拟器6的Instagram关注任务线程 - 当前并发: 1/1
2025-07-29 09:35:23 - Emulator - INFO - 模拟器状态变化 | emulator_id: 6 | old_state: 启动中 | new_state: 运行中
2025-07-29 09:35:23 - Emulator - INFO - 模拟器启动成功 | emulator_id: 6 | running_count: 1
2025-07-29 09:35:23 - TaskActivityHeartbeatManager - INFO - 模拟器 6 已添加到任务活动监控，失败计数: 0
2025-07-29 09:35:23 - MainWindowV2 - INFO - 任务完成: 模拟器6, 任务start
2025-07-29 09:35:23 - MainWindowV2 - INFO - 模拟器6启动成功
2025-07-29 09:35:23 - InstagramTaskThread - INFO - [模拟器6] 开始等待启动完成
2025-07-29 09:35:23 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=6, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-29 09:35:23 - InstagramTaskThread - INFO - [模拟器6] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-29 09:35:23 - InstagramTaskThread - INFO - [模拟器6] 开始窗口排列
2025-07-29 09:35:23 - WindowArrangementManager - INFO - 模拟器6启动完成，立即触发窗口排列
2025-07-29 09:35:23 - MainWindowV2 - WARNING - 未找到模拟器6，无法更新状态
2025-07-29 09:35:23 - MainWindowV2 - INFO - 模拟器6: 启动中 -> 运行中
2025-07-29 09:35:23 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 2 | queued: 1 | starting: 0 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 50.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-29 09:35:23 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中0个, 运行1个, 失败0个 (并发槽位:1/1)
2025-07-29 09:35:23 - MainWindowV2 - INFO - 模拟器6: 启动中 -> 运行中, PID: 21468
2025-07-29 09:35:23 - MainWindowV2 - INFO - 模拟器6: 启动中 -> 运行中
2025-07-29 09:35:25 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-29 09:35:25 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-29 09:35:25 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-29 09:35:25 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-29 09:35:25 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-29 09:35:25 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-29 09:35:25 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-6' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-29 09:35:25 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-29 09:35:25 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-29 09:35:25 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-6' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-29 09:35:25 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-29 09:35:25 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-29 09:35:29 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-29 09:35:29 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-29 09:35:29 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 6
2025-07-29 09:35:29 - MainWindowV2 - WARNING - 模拟器6心跳状态更新未产生变化
2025-07-29 09:35:29 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-29 09:35:31 - InstagramTaskThread - INFO - [模拟器6] 窗口排列完成
2025-07-29 09:35:31 - InstagramFollowTaskThread - INFO - [模拟器6] 开始执行Instagram直接关注任务
2025-07-29 09:35:31 - InstagramDMTask - INFO - [模拟器6] 雷电模拟器API初始化成功
2025-07-29 09:35:31 - InstagramDMTask - INFO - [模拟器6] 模拟器路径: G:/leidian/LDPlayer9
2025-07-29 09:35:31 - InstagramDMTask - INFO - [模拟器6] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-29 09:35:31 - InstagramDMTask - INFO - [模拟器6] 已设置ld.emulator_id = 6
2025-07-29 09:35:31 - InstagramDMTask - INFO - [模拟器6] Instagram任务配置加载完成: 私信数量=2, 延迟=2-200ms
2025-07-29 09:35:31 - InstagramDMTask - INFO - [模拟器6] Instagram任务配置热加载观察者已注册
2025-07-29 09:35:31 - InstagramDMTask - INFO - [模拟器6] Instagram私信任务执行器初始化完成
2025-07-29 09:35:31 - InstagramFollowTask - INFO - [模拟器6] Instagram关注任务配置加载完成
2025-07-29 09:35:31 - InstagramFollowTask - INFO - [模拟器6] Instagram关注任务执行器初始化完成
2025-07-29 09:35:31 - InstagramFollowTask - INFO - [模拟器6] 关注模式已设置为: direct
2025-07-29 09:35:31 - InstagramFollowTask - INFO - [模拟器6] 开始执行Instagram关注任务
2025-07-29 09:35:31 - InstagramFollowTask - WARNING - [模拟器6] 任务开始时间未由线程传递，在此设置
2025-07-29 09:35:31 - InstagramFollowTask - INFO - [模拟器6] 任务超时设置: 666秒，已运行: 0.00秒
2025-07-29 09:35:31 - InstagramDMTask - INFO - [模拟器6] 模拟器Android系统运行正常，桌面稳定
2025-07-29 09:35:31 - InstagramDMTask - INFO - [模拟器6] 开始检查应用安装状态
2025-07-29 09:35:31 - InstagramDMTask - INFO - [模拟器6] ✅ V2Ray已安装，版本: 1.1.12
2025-07-29 09:35:31 - InstagramDMTask - INFO - [模拟器6] ✅ Instagram已安装，版本: 321.0.0.39.106
2025-07-29 09:35:31 - InstagramDMTask - INFO - [模拟器6] 📊 应用安装状态检测结果:
2025-07-29 09:35:31 - InstagramDMTask - INFO - [模拟器6] ✅ 所有必要应用已安装
2025-07-29 09:35:31 - InstagramDMTask - INFO - [模拟器6] 开始启动V2Ray应用
2025-07-29 09:35:31 - InstagramDMTask - INFO - [模拟器6] V2Ray启动命令执行成功，等待应用加载
2025-07-29 09:35:31 - InstagramDMTask - INFO - [模拟器6] V2Ray应用启动结果: 成功
2025-07-29 09:35:34 - InstagramDMTask - INFO - [模拟器6] ✅ V2Ray应用启动成功
2025-07-29 09:35:34 - InstagramDMTask - INFO - [模拟器6] 开始检查V2Ray节点列表状态
2025-07-29 09:35:36 - InstagramDMTask - INFO - [模拟器6] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-29 09:35:36 - InstagramDMTask - INFO - [模拟器6] 开始连接V2Ray节点
2025-07-29 09:35:37 - InstagramDMTask - INFO - [模拟器6] 当前连接状态: 未连接
2025-07-29 09:35:37 - InstagramDMTask - INFO - [模拟器6] V2Ray节点未连接，开始连接
2025-07-29 09:35:39 - InstagramDMTask - INFO - [模拟器6] 已点击连接按钮，等待连接完成
2025-07-29 09:35:41 - InstagramDMTask - INFO - [模拟器6] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-29 09:35:41 - InstagramDMTask - INFO - [模拟器6] V2Ray节点连接成功
2025-07-29 09:35:41 - InstagramDMTask - INFO - [模拟器6] 开始测试V2Ray节点延迟
2025-07-29 09:35:41 - InstagramDMTask - INFO - [模拟器6] 开始V2Ray节点延迟测试
2025-07-29 09:35:42 - InstagramDMTask - INFO - [模拟器6] 当前测试状态: 已连接，点击测试连接
2025-07-29 09:35:42 - InstagramDMTask - INFO - [模拟器6] 点击开始延迟测试
2025-07-29 09:35:42 - InstagramDMTask - INFO - [模拟器6] 已点击测试按钮，等待测试结果
2025-07-29 09:35:44 - InstagramDMTask - INFO - [模拟器6] 测试状态监控 (1/30): 连接成功：延时 590 毫秒
2025-07-29 09:35:44 - InstagramDMTask - INFO - [模拟器6] ✅ V2Ray节点延迟测试成功: 连接成功：延时 590 毫秒
2025-07-29 09:35:44 - InstagramDMTask - INFO - [模拟器6] 等待5秒后进入下一阶段
2025-07-29 09:35:49 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-29 09:35:49 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-29 09:35:49 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 6
2025-07-29 09:35:49 - MainWindowV2 - WARNING - 模拟器6心跳状态更新未产生变化
2025-07-29 09:35:49 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-29 09:35:49 - InstagramDMTask - INFO - [模拟器6] 开始启动Instagram应用
2025-07-29 09:35:49 - InstagramDMTask - INFO - [模拟器6] Instagram启动命令执行成功，等待应用加载
2025-07-29 09:35:52 - InstagramDMTask - INFO - [模拟器6] ✅ Instagram应用启动命令执行完成
2025-07-29 09:35:52 - InstagramDMTask - INFO - [模拟器6] Instagram启动检测 第1/5次
2025-07-29 09:35:56 - InstagramDMTask - INFO - [模拟器6] Instagram启动检测中... 已等待4.1秒
2025-07-29 09:35:59 - InstagramDMTask - INFO - [模拟器6] ✅ 批量验证成功
2025-07-29 09:35:59 - InstagramDMTask - INFO - [模拟器6] ✅ 验证成功
2025-07-29 09:35:59 - InstagramFollowTask - INFO - [模拟器6] Instagram页面状态检测结果: 正常-在主页面
2025-07-29 09:35:59 - InstagramFollowTask - INFO - [模拟器6] ✅ Instagram已在主页面，可以继续执行任务
2025-07-29 09:35:59 - InstagramFollowTask - INFO - [模拟器6] 开始执行关注任务，模式: direct
2025-07-29 09:35:59 - InstagramFollowTask - INFO - [模拟器6] 开始执行【模式一：直接关注模式】
2025-07-29 09:35:59 - InstagramFollowTask - INFO - [模拟器6] 开始【模式一：直接关注循环】，目标关注数: 5
2025-07-29 09:35:59 - InstagramFollowTask - INFO - [模拟器6] 开始处理目标用户: ramber_dogfield_official (已关注: 0/5)
2025-07-29 09:35:59 - InstagramFollowTask - INFO - [模拟器6] 开始处理用户：ramber_dogfield_official
2025-07-29 09:35:59 - InstagramFollowTask - INFO - [模拟器6] 跳转到用户主页：ramber_dogfield_official
2025-07-29 09:35:59 - InstagramFollowTask - INFO - [模拟器6] 开始检测用户信息
2025-07-29 09:36:04 - InstagramFollowTask - INFO - [模拟器6] 🔍 关注状态检测: 关注
2025-07-29 09:36:04 - InstagramFollowTask - INFO - [模拟器6] ✅ 标题栏验证成功，当前在用户 ramber_dogfield_official 的资料页
2025-07-29 09:36:04 - InstagramFollowTask - INFO - [模拟器6] 用户信息检测完成，耗时4.82秒
2025-07-29 09:36:04 - InstagramFollowTask - INFO - [模拟器6] 该用户未关注,开始关注
2025-07-29 09:36:06 - InstagramFollowTask - INFO - [模拟器6] 关注完成,已关注 1 / 5
2025-07-29 09:36:06 - InstagramFollowTask - INFO - [模拟器6] 直接关注任务状态更新信号已发送: 关注中 (1/5)
2025-07-29 09:36:06 - MainWindowV2 - INFO - 模拟器6的Instagram任务状态已更新: 关注中 (1/5)
2025-07-29 09:36:06 - InstagramFollowTask - INFO - [模拟器6] ⏱️ 切换用户延迟: 22毫秒
2025-07-29 09:36:06 - InstagramFollowTask - INFO - [模拟器6] 开始处理目标用户: 1225kasumi (已关注: 1/5)
2025-07-29 09:36:06 - InstagramFollowTask - INFO - [模拟器6] 开始处理用户：1225kasumi
2025-07-29 09:36:06 - InstagramFollowTask - INFO - [模拟器6] 跳转到用户主页：1225kasumi
2025-07-29 09:36:06 - InstagramFollowTask - INFO - [模拟器6] 开始检测用户信息
2025-07-29 09:36:09 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-29 09:36:09 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-29 09:36:09 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 6
2025-07-29 09:36:09 - MainWindowV2 - WARNING - 模拟器6心跳状态更新未产生变化
2025-07-29 09:36:09 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-29 09:36:11 - InstagramFollowTask - INFO - [模拟器6] 🔍 关注状态检测: 关注
2025-07-29 09:36:11 - InstagramFollowTask - INFO - [模拟器6] ✅ 标题栏验证成功，当前在用户 1225kasumi 的资料页
2025-07-29 09:36:11 - InstagramFollowTask - INFO - [模拟器6] 用户信息检测完成，耗时4.59秒
2025-07-29 09:36:11 - InstagramFollowTask - INFO - [模拟器6] 该用户未关注,开始关注
2025-07-29 09:36:14 - InstagramFollowTask - INFO - [模拟器6] 关注完成,已关注 2 / 5
2025-07-29 09:36:14 - InstagramFollowTask - INFO - [模拟器6] 直接关注任务状态更新信号已发送: 关注中 (2/5)
2025-07-29 09:36:14 - InstagramFollowTask - INFO - [模拟器6] ⏱️ 切换用户延迟: 5毫秒
2025-07-29 09:36:14 - MainWindowV2 - INFO - 模拟器6的Instagram任务状态已更新: 关注中 (2/5)
2025-07-29 09:36:14 - InstagramFollowTask - INFO - [模拟器6] 开始处理目标用户: mame_info (已关注: 2/5)
2025-07-29 09:36:14 - InstagramFollowTask - INFO - [模拟器6] 开始处理用户：mame_info
2025-07-29 09:36:14 - InstagramFollowTask - INFO - [模拟器6] 跳转到用户主页：mame_info
2025-07-29 09:36:14 - InstagramFollowTask - INFO - [模拟器6] 开始检测用户信息
2025-07-29 09:36:17 - InstagramFollowTask - INFO - [模拟器6] 🔍 关注状态检测: 关注
2025-07-29 09:36:17 - InstagramFollowTask - INFO - [模拟器6] ✅ 标题栏验证成功，当前在用户 mame_info 的资料页
2025-07-29 09:36:17 - InstagramFollowTask - INFO - [模拟器6] 用户信息检测完成，耗时2.79秒
2025-07-29 09:36:17 - InstagramFollowTask - INFO - [模拟器6] 该用户未关注,开始关注
2025-07-29 09:36:20 - InstagramFollowTask - INFO - [模拟器6] 关注完成,已关注 3 / 5
2025-07-29 09:36:20 - InstagramFollowTask - INFO - [模拟器6] 直接关注任务状态更新信号已发送: 关注中 (3/5)
2025-07-29 09:36:20 - MainWindowV2 - INFO - 模拟器6的Instagram任务状态已更新: 关注中 (3/5)
2025-07-29 09:36:20 - InstagramFollowTask - INFO - [模拟器6] ⏱️ 切换用户延迟: 15毫秒
2025-07-29 09:36:20 - InstagramFollowTask - INFO - [模拟器6] 开始处理目标用户: yupi.grams (已关注: 3/5)
2025-07-29 09:36:20 - InstagramFollowTask - INFO - [模拟器6] 开始处理用户：yupi.grams
2025-07-29 09:36:20 - InstagramFollowTask - INFO - [模拟器6] 跳转到用户主页：yupi.grams
2025-07-29 09:36:20 - InstagramFollowTask - INFO - [模拟器6] 开始检测用户信息
2025-07-29 09:36:25 - InstagramFollowTask - INFO - [模拟器6] 🔍 关注状态检测: 关注
2025-07-29 09:36:25 - InstagramFollowTask - INFO - [模拟器6] ✅ 标题栏验证成功，当前在用户 yupi.grams 的资料页
2025-07-29 09:36:25 - InstagramFollowTask - INFO - [模拟器6] 用户信息检测完成，耗时4.87秒
2025-07-29 09:36:25 - InstagramFollowTask - INFO - [模拟器6] 该用户未关注,开始关注
2025-07-29 09:36:27 - InstagramFollowTask - INFO - [模拟器6] 关注完成,已关注 4 / 5
2025-07-29 09:36:27 - InstagramFollowTask - INFO - [模拟器6] 直接关注任务状态更新信号已发送: 关注中 (4/5)
2025-07-29 09:36:27 - MainWindowV2 - INFO - 模拟器6的Instagram任务状态已更新: 关注中 (4/5)
2025-07-29 09:36:27 - InstagramFollowTask - INFO - [模拟器6] ⏱️ 切换用户延迟: 15毫秒
2025-07-29 09:36:27 - InstagramFollowTask - INFO - [模拟器6] 开始处理目标用户: amyypatton (已关注: 4/5)
2025-07-29 09:36:27 - InstagramFollowTask - INFO - [模拟器6] 开始处理用户：amyypatton
2025-07-29 09:36:27 - InstagramFollowTask - INFO - [模拟器6] 跳转到用户主页：amyypatton
2025-07-29 09:36:27 - InstagramFollowTask - INFO - [模拟器6] 开始检测用户信息
2025-07-29 09:36:29 - InstagramFollowTask - INFO - [模拟器6] 检测到Instagram关注配置变化: instagram_follow.direct_follow_count = 5 → 2
2025-07-29 09:36:29 - InstagramFollowTask - INFO - [模拟器6] Instagram关注任务配置加载完成
2025-07-29 09:36:29 - InstagramFollowTask - INFO - [模拟器6] Instagram关注任务配置已热加载更新
2025-07-29 09:36:29 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-29 09:36:29 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-29 09:36:29 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 6
2025-07-29 09:36:29 - MainWindowV2 - WARNING - 模拟器6心跳状态更新未产生变化
2025-07-29 09:36:29 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-29 09:36:32 - InstagramFollowTask - INFO - [模拟器6] 🔍 关注状态检测: 关注
2025-07-29 09:36:32 - InstagramFollowTask - INFO - [模拟器6] ✅ 标题栏验证成功，当前在用户 amyypatton 的资料页
2025-07-29 09:36:32 - InstagramFollowTask - INFO - [模拟器6] 🔍 蓝V检测: 找到认证标识
2025-07-29 09:36:32 - InstagramFollowTask - INFO - [模拟器6] 用户信息检测完成，耗时4.12秒
2025-07-29 09:36:32 - InstagramFollowTask - INFO - [模拟器6] 📊 目标用户处理结果: 蓝V认证账户（已跳过）
2025-07-29 09:36:32 - InstagramFollowTask - INFO - [模拟器6] 任务完成,退出循环.--任务进度 :4 / 2 耗时: 32.84秒
2025-07-29 09:36:32 - InstagramFollowTask - INFO - [模拟器6] 【模式一：直接关注循环】完成，成功关注: 4, 跳过蓝V: 1, 跳过私密: 0
2025-07-29 09:36:32 - InstagramDMTask - INFO - [模拟器6] Instagram任务配置观察者已注销
2025-07-29 09:36:32 - InstagramDMTask - INFO - [模拟器6] 开始任务完成后清理工作
2025-07-29 09:36:32 - TaskActivityHeartbeatManager - INFO - 模拟器 6 已从任务活动监控移除
2025-07-29 09:36:32 - InstagramDMTask - INFO - [模拟器6] 心跳监控已移除
2025-07-29 09:36:32 - InstagramDMTask - INFO - [模拟器6] 准备关闭模拟器
2025-07-29 09:36:32 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 6 | remaining_running: 0
2025-07-29 09:36:32 - Emulator - INFO - 模拟器停止成功 | emulator_id: 6
2025-07-29 09:36:32 - InstagramDMTask - INFO - [模拟器6] 模拟器已成功关闭
2025-07-29 09:36:32 - MainWindowV2 - INFO - 任务完成: 模拟器6, 任务stop
2025-07-29 09:36:32 - InstagramDMTask - INFO - [模拟器6] 任务完成后清理工作完成
2025-07-29 09:36:32 - MainWindowV2 - INFO - 模拟器6停止成功
2025-07-29 09:36:32 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=6, 任务类型=stop, 结果={'result': '停止成功', 'message': ''}
2025-07-29 09:36:32 - InstagramFollowTaskThread - INFO - [模拟器6] Instagram直接关注任务执行成功
2025-07-29 09:36:32 - WindowArrangementManager - INFO - 跳过非启动任务或失败任务: 任务类型=stop, 结果=停止成功
2025-07-29 09:36:33 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器7状态变化: 排队中 -> 启动中
2025-07-29 09:36:33 - Emulator - INFO - 模拟器状态变化 | emulator_id: 7 | old_state: 排队中 | new_state: 启动中
2025-07-29 09:36:33 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器7状态变化: 排队中 -> 启动中
2025-07-29 09:36:33 - MainWindowV2 - INFO - 模拟器7: 排队中 -> 启动中
2025-07-29 09:36:33 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 7
2025-07-29 09:36:33 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 2 | queued: 0 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-29 09:36:33 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/1)
2025-07-29 09:36:33 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 7
2025-07-29 09:36:43 - Emulator - INFO - Android系统启动完成 | emulator_id: 7 | elapsed_time: 10.0秒
2025-07-29 09:36:43 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器7状态变化: 启动中 -> 运行中
2025-07-29 09:36:43 - Emulator - INFO - 模拟器状态变化 | emulator_id: 7 | old_state: 启动中 | new_state: 运行中
2025-07-29 09:36:43 - InstagramFollowTaskManager - INFO - 启动模拟器7的Instagram关注任务线程 - 当前并发: 2/1
2025-07-29 09:36:43 - TaskActivityHeartbeatManager - INFO - 模拟器 7 已添加到任务活动监控，失败计数: 0
2025-07-29 09:36:43 - Emulator - INFO - 模拟器启动成功 | emulator_id: 7 | running_count: 1
2025-07-29 09:36:43 - MainWindowV2 - INFO - 任务完成: 模拟器7, 任务start
2025-07-29 09:36:43 - MainWindowV2 - INFO - 模拟器7启动成功
2025-07-29 09:36:43 - InstagramTaskThread - INFO - [模拟器7] 开始等待启动完成
2025-07-29 09:36:43 - InstagramTaskThread - INFO - [模拟器7] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-29 09:36:43 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=7, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-29 09:36:43 - InstagramTaskThread - INFO - [模拟器7] 开始窗口排列
2025-07-29 09:36:43 - WindowArrangementManager - INFO - 模拟器7启动完成，立即触发窗口排列
2025-07-29 09:36:43 - MainWindowV2 - WARNING - 未找到模拟器7，无法更新状态
2025-07-29 09:36:43 - MainWindowV2 - INFO - 模拟器7: 启动中 -> 运行中
2025-07-29 09:36:43 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 2 | queued: 0 | starting: 0 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 50.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-29 09:36:43 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中0个, 运行1个, 失败0个 (并发槽位:1/1)
2025-07-29 09:36:43 - MainWindowV2 - INFO - 模拟器7: 启动中 -> 运行中, PID: 18580
2025-07-29 09:36:43 - MainWindowV2 - INFO - 模拟器7: 启动中 -> 运行中
2025-07-29 09:36:44 - StartupManager - INFO - 调度器已停止
2025-07-29 09:36:45 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-29 09:36:45 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-29 09:36:45 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-29 09:36:45 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-29 09:36:45 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-29 09:36:45 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-29 09:36:45 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-7' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-29 09:36:45 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-29 09:36:45 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-29 09:36:45 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-7' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-29 09:36:45 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-29 09:36:45 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
