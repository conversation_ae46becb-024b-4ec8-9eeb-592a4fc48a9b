2025-07-29 09:26:08 - root - INFO - 简化日志系统初始化完成
2025-07-29 09:26:08 - main - INFO - 应用程序启动
2025-07-29 09:26:08 - __main__ - INFO - Qt应用程序已创建
2025-07-29 09:26:08 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-29 09:26:08 - __main__ - INFO - 统一配置管理器已创建
2025-07-29 09:26:08 - DatabaseManager - INFO - SQLite连接已配置WAL模式
2025-07-29 09:26:08 - DatabaseManager - ERROR - 检查表结构失败: 1
2025-07-29 09:26:08 - DatabaseManager - INFO - 数据库表结构创建完成
2025-07-29 09:26:08 - DatabaseManager - INFO - 数据库初始化完成: E:\VScodexiangmu\insleidian2\emulator_system.db
2025-07-29 09:26:08 - EmulatorRepository - INFO - 模拟器数据仓库初始化完成
2025-07-29 09:26:08 - StartupManager - INFO - 统一启动管理器初始化完成
2025-07-29 09:26:08 - TaskActivityHeartbeatManager - INFO - 心跳检测已启用
2025-07-29 09:26:08 - TaskActivityHeartbeatManager - INFO - 任务活动检测心跳管理器初始化完成
2025-07-29 09:26:08 - TaskActivityHeartbeatManager - INFO - 心跳监控服务已启动，检测间隔: 20秒
2025-07-29 09:26:08 - UnifiedEmulatorManager - INFO - 统一模拟器管理器初始化完成
2025-07-29 09:26:08 - __main__ - INFO - 修复版异步桥梁已创建
2025-07-29 09:26:09 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-29 09:26:09 - BasicConfigUI - INFO - 基础配置信号连接完成
2025-07-29 09:26:09 - BasicConfigUI - INFO - 基础配置UI已注册为配置观察者，支持热加载
2025-07-29 09:26:09 - BasicConfigUI - INFO - 浏览按钮事件连接完成
2025-07-29 09:26:09 - BasicConfigUI - INFO - 基础配置UI初始化完成
2025-07-29 09:26:09 - InstagramDMUI - INFO - Instagram私信配置加载完成
2025-07-29 09:26:09 - InstagramFollowUI - INFO - Instagram关注配置加载完成
2025-07-29 09:26:09 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 程序启动)
2025-07-29 09:26:09 - MainWindowV2 - INFO - 事件驱动日志更新已启动 - 表格运行日志列实时更新（替代定时器）
2025-07-29 09:26:09 - MainWindowV2 - INFO - Instagram任务状态更新信号已连接
2025-07-29 09:26:09 - __main__ - INFO - UI主窗口已创建
2025-07-29 09:26:09 - MainWindowV2 - INFO - UI主窗口已创建
2025-07-29 09:26:09 - __main__ - INFO - 主窗口已显示
2025-07-29 09:26:09 - WindowArrangementManager - INFO - 窗口排列管理器初始化完成
2025-07-29 09:26:09 - FixedAsyncBridge - INFO - 任务完成信号已连接到窗口排列管理器
2025-07-29 09:26:09 - FixedAsyncBridge - INFO - 任务完成信号、状态变化信号、心跳监控信号和窗口排列信号已连接到UI
2025-07-29 09:26:09 - FixedAsyncBridge - INFO - 采用事件驱动方式，无需监控器
2025-07-29 09:26:09 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-29 09:26:09 - AsyncEventLoopManager - INFO - 专用异步事件循环已启动
2025-07-29 09:26:09 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-29 09:26:09 - __main__ - INFO - UI层和业务层已连接
2025-07-29 09:26:09 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-29 09:26:09 - __main__ - INFO - 启动Qt事件循环
2025-07-29 09:26:09 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-29 09:26:09 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-29 09:26:09 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 路径变化)
2025-07-29 09:26:09 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-29 09:26:09 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-29 09:26:09 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-29 09:26:09 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-29 09:26:09 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-29 09:26:10 - App - INFO - ldconsole命令执行成功，输出长度: 48394
2025-07-29 09:26:10 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-29 09:26:10 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-29 09:26:10 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-29 09:26:10 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-29 09:26:10 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.42s | count: 1229
2025-07-29 09:26:10 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-29 09:26:10 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-29 09:26:10 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-29 09:26:10 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-29 09:26:10 - App - INFO - ldconsole命令执行成功，输出长度: 48394
2025-07-29 09:26:10 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-29 09:26:10 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-29 09:26:10 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-29 09:26:10 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-29 09:26:10 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.33s | count: 1229
2025-07-29 09:26:10 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-29 09:26:10 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-29 09:26:10 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-29 09:26:10 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-29 09:26:10 - ConfigHotReloadService - INFO - 配置热加载服务初始化完成（Qt定时器模式）
2025-07-29 09:26:10 - ConfigHotReloadService - INFO - 配置热加载服务已启动（Qt定时器模式）
2025-07-29 09:26:10 - FixedAsyncBridge - INFO - 后台服务已启动
2025-07-29 09:26:10 - __main__ - INFO - 后台服务已启动
2025-07-29 09:26:10 - __main__ - INFO - 延迟启动服务完成
2025-07-29 09:26:16 - FixedAsyncBridge - INFO - 执行异步操作: instagram_follow_fans_task
2025-07-29 09:26:16 - MainWindowV2 - INFO - Instagram关注粉丝任务已启动，涉及3个模拟器
2025-07-29 09:26:16 - FixedAsyncBridge - INFO - 异步桥接器: 处理Instagram关注粉丝任务请求
2025-07-29 09:26:16 - MainWindowV2 - INFO - 用户启动Instagram关注粉丝任务，模拟器数量: 3
2025-07-29 09:26:16 - FixedAsyncBridge - INFO - 开始处理Instagram关注粉丝任务（线程池模式），模拟器数量: 3
2025-07-29 09:26:16 - FixedAsyncBridge - INFO - 使用批量启动功能启动模拟器: [3, 4, 6]
2025-07-29 09:26:16 - FixedAsyncBridge - INFO - 异步桥接器: 处理批量启动请求，模拟器数量: 3
2025-07-29 09:26:16 - StartupManager - INFO - 批量启动请求 | count: 3
2025-07-29 09:26:16 - StartupManager - INFO - 启动调度器已启动
2025-07-29 09:26:16 - FixedAsyncBridge - INFO - 异步桥接器: 批量启动请求已处理，状态: queued
2025-07-29 09:26:16 - FixedAsyncBridge - INFO - 模拟器批量启动成功，开始创建关注任务线程池
2025-07-29 09:26:16 - InstagramTaskManager - INFO - Instagram任务管理器已连接模拟器状态变化信号
2025-07-29 09:26:16 - InstagramTaskManager - INFO - Instagram任务管理器初始化完成，最大并发: 1
2025-07-29 09:26:16 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器初始化完成，模式: fans
2025-07-29 09:26:16 - InstagramTaskManager - INFO - 开始启动Instagram任务管理器，模拟器数量: 3
2025-07-29 09:26:16 - InstagramFollowTaskThread - INFO - [模拟器3] Instagram关注任务线程初始化完成，模式: fans
2025-07-29 09:26:16 - InstagramFollowTaskManager - INFO - 为模拟器 3 创建Instagram关注任务线程，模式: fans
2025-07-29 09:26:16 - InstagramFollowTaskThread - INFO - [模拟器4] Instagram关注任务线程初始化完成，模式: fans
2025-07-29 09:26:16 - InstagramFollowTaskManager - INFO - 为模拟器 4 创建Instagram关注任务线程，模式: fans
2025-07-29 09:26:16 - InstagramFollowTaskThread - INFO - [模拟器6] Instagram关注任务线程初始化完成，模式: fans
2025-07-29 09:26:16 - InstagramFollowTaskManager - INFO - 为模拟器 6 创建Instagram关注任务线程，模式: fans
2025-07-29 09:26:16 - InstagramTaskManager - INFO - 线程池创建完成: 成功创建3个Instagram线程
2025-07-29 09:26:16 - FixedAsyncBridge - INFO - 异步桥接器: Instagram关注粉丝任务请求已处理，状态: started
2025-07-29 09:26:16 - MainWindowV2 - INFO - 3个模拟器: 未知 -> 排队中
2025-07-29 09:26:16 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器3状态变化: 排队中 -> 启动中
2025-07-29 09:26:16 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器3状态变化: 排队中 -> 启动中
2025-07-29 09:26:16 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 3 | starting: 0 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 0 | max_concurrent: 1
2025-07-29 09:26:16 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 3
2025-07-29 09:26:16 - MainWindowV2 - INFO - 启动进度: 排队3个, 启动中0个, 运行0个, 失败0个 (并发槽位:0/1)
2025-07-29 09:26:16 - FixedAsyncBridge - INFO - 异步操作完成: start_batch
2025-07-29 09:26:16 - MainWindowV2 - INFO - 批量启动完成: 0/3 成功
2025-07-29 09:26:16 - MainWindowV2 - INFO - 批量启动操作完成 | success: 0 | total: 3
2025-07-29 09:26:16 - FixedAsyncBridge - INFO - 异步操作完成: instagram_follow_fans_task
2025-07-29 09:26:16 - Emulator - INFO - 模拟器状态变化 | emulator_id: 3 | old_state: 排队中 | new_state: 启动中
2025-07-29 09:26:16 - MainWindowV2 - INFO - 模拟器3: 排队中 -> 启动中
2025-07-29 09:26:16 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 2 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-29 09:26:16 - MainWindowV2 - INFO - 启动进度: 排队2个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/1)
2025-07-29 09:26:16 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 3
2025-07-29 09:26:26 - Emulator - INFO - Android系统启动完成 | emulator_id: 3 | elapsed_time: 10.0秒
2025-07-29 09:26:26 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器3状态变化: 启动中 -> 运行中
2025-07-29 09:26:26 - InstagramFollowTaskManager - INFO - 启动模拟器3的Instagram关注任务线程 - 当前并发: 1/1
2025-07-29 09:26:26 - Emulator - INFO - 模拟器状态变化 | emulator_id: 3 | old_state: 启动中 | new_state: 运行中
2025-07-29 09:26:26 - Emulator - INFO - 模拟器启动成功 | emulator_id: 3 | running_count: 1
2025-07-29 09:26:26 - TaskActivityHeartbeatManager - INFO - 模拟器 3 已添加到任务活动监控，失败计数: 0
2025-07-29 09:26:26 - MainWindowV2 - INFO - 任务完成: 模拟器3, 任务start
2025-07-29 09:26:26 - InstagramTaskThread - INFO - [模拟器3] 开始等待启动完成
2025-07-29 09:26:26 - MainWindowV2 - INFO - 模拟器3启动成功
2025-07-29 09:26:26 - InstagramTaskThread - INFO - [模拟器3] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-29 09:26:26 - InstagramTaskThread - INFO - [模拟器3] 开始窗口排列
2025-07-29 09:26:26 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=3, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-29 09:26:26 - WindowArrangementManager - INFO - 模拟器3启动完成，立即触发窗口排列
2025-07-29 09:26:26 - MainWindowV2 - WARNING - 未找到模拟器3，无法更新状态
2025-07-29 09:26:26 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中
2025-07-29 09:26:26 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 2 | starting: 0 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 33.33333333333333 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-29 09:26:26 - MainWindowV2 - INFO - 启动进度: 排队2个, 启动中0个, 运行1个, 失败0个 (并发槽位:1/1)
2025-07-29 09:26:27 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中, PID: 1380
2025-07-29 09:26:27 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中
2025-07-29 09:26:28 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-29 09:26:28 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-29 09:26:28 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-29 09:26:28 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-29 09:26:28 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-29 09:26:28 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-29 09:26:28 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-29 09:26:28 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-29 09:26:28 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-29 09:26:28 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-29 09:26:28 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-29 09:26:29 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-29 09:26:29 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-29 09:26:29 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-29 09:26:29 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-29 09:26:29 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-29 09:26:29 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-29 09:26:34 - InstagramTaskThread - INFO - [模拟器3] 窗口排列完成
2025-07-29 09:26:34 - InstagramFollowTaskThread - INFO - [模拟器3] 开始执行Instagram关注粉丝任务
2025-07-29 09:26:35 - InstagramDMTask - INFO - [模拟器3] 雷电模拟器API初始化成功
2025-07-29 09:26:35 - InstagramDMTask - INFO - [模拟器3] 模拟器路径: G:/leidian/LDPlayer9
2025-07-29 09:26:35 - InstagramDMTask - INFO - [模拟器3] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-29 09:26:35 - InstagramDMTask - INFO - [模拟器3] 已设置ld.emulator_id = 3
2025-07-29 09:26:35 - InstagramDMTask - INFO - [模拟器3] Instagram任务配置加载完成: 私信数量=2, 延迟=2-200ms
2025-07-29 09:26:35 - InstagramDMTask - INFO - [模拟器3] Instagram任务配置热加载观察者已注册
2025-07-29 09:26:35 - InstagramDMTask - INFO - [模拟器3] Instagram私信任务执行器初始化完成
2025-07-29 09:26:35 - InstagramFollowTask - INFO - [模拟器3] Instagram关注任务配置加载完成
2025-07-29 09:26:35 - InstagramFollowTask - INFO - [模拟器3] Instagram关注任务执行器初始化完成
2025-07-29 09:26:35 - InstagramFollowTask - INFO - [模拟器3] 关注模式已设置为: fans
2025-07-29 09:26:35 - InstagramFollowTask - INFO - [模拟器3] 开始执行Instagram关注任务
2025-07-29 09:26:35 - InstagramFollowTask - WARNING - [模拟器3] 任务开始时间未由线程传递，在此设置
2025-07-29 09:26:35 - InstagramFollowTask - INFO - [模拟器3] 任务超时设置: 666秒，已运行: 0.00秒
2025-07-29 09:26:35 - InstagramDMTask - INFO - [模拟器3] 模拟器Android系统运行正常，桌面稳定
2025-07-29 09:26:35 - InstagramDMTask - INFO - [模拟器3] 开始检查应用安装状态
2025-07-29 09:26:35 - InstagramDMTask - INFO - [模拟器3] ✅ V2Ray已安装，版本: 1.1.12
2025-07-29 09:26:35 - InstagramDMTask - INFO - [模拟器3] ✅ Instagram已安装，版本: 278.0.0.21.117
2025-07-29 09:26:35 - InstagramDMTask - INFO - [模拟器3] 📊 应用安装状态检测结果:
2025-07-29 09:26:35 - InstagramDMTask - INFO - [模拟器3] ✅ 所有必要应用已安装
2025-07-29 09:26:35 - InstagramDMTask - INFO - [模拟器3] 开始启动V2Ray应用
2025-07-29 09:26:35 - InstagramDMTask - INFO - [模拟器3] V2Ray启动命令执行成功，等待应用加载
2025-07-29 09:26:35 - InstagramDMTask - INFO - [模拟器3] V2Ray应用启动结果: 成功
2025-07-29 09:26:38 - InstagramDMTask - INFO - [模拟器3] ✅ V2Ray应用启动成功
2025-07-29 09:26:38 - InstagramDMTask - INFO - [模拟器3] 开始检查V2Ray节点列表状态
2025-07-29 09:26:40 - InstagramDMTask - INFO - [模拟器3] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-29 09:26:40 - InstagramDMTask - INFO - [模拟器3] 开始连接V2Ray节点
2025-07-29 09:26:41 - InstagramDMTask - INFO - [模拟器3] 当前连接状态: 未连接
2025-07-29 09:26:41 - InstagramDMTask - INFO - [模拟器3] V2Ray节点未连接，开始连接
2025-07-29 09:26:42 - InstagramDMTask - INFO - [模拟器3] 已点击连接按钮，等待连接完成
2025-07-29 09:26:44 - InstagramDMTask - INFO - [模拟器3] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-29 09:26:44 - InstagramDMTask - INFO - [模拟器3] V2Ray节点连接成功
2025-07-29 09:26:44 - InstagramDMTask - INFO - [模拟器3] 开始测试V2Ray节点延迟
2025-07-29 09:26:44 - InstagramDMTask - INFO - [模拟器3] 开始V2Ray节点延迟测试
2025-07-29 09:26:46 - InstagramDMTask - INFO - [模拟器3] 当前测试状态: 已连接，点击测试连接
2025-07-29 09:26:46 - InstagramDMTask - INFO - [模拟器3] 点击开始延迟测试
2025-07-29 09:26:46 - InstagramDMTask - INFO - [模拟器3] 已点击测试按钮，等待测试结果
2025-07-29 09:26:47 - InstagramDMTask - INFO - [模拟器3] 测试状态监控 (1/30): 失败：: io: read/write on closed pipe
2025-07-29 09:26:47 - InstagramDMTask - ERROR - [模拟器3] ❌ V2Ray节点延迟测试失败 (1/3): 失败：: io: read/write on closed pipe
2025-07-29 09:26:47 - InstagramDMTask - INFO - [模拟器3] 点击失败状态重置UI
2025-07-29 09:26:47 - InstagramDMTask - INFO - [模拟器3] 已点击失败状态，等待UI重置
2025-07-29 09:26:48 - InstagramDMTask - INFO - [模拟器3] 继续等待测试结果 (1/3)
2025-07-29 09:26:48 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-29 09:26:48 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-29 09:26:48 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-29 09:26:48 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-29 09:26:48 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-29 09:26:49 - InstagramDMTask - ERROR - [模拟器3] ❌ V2Ray节点延迟测试失败 (2/3): 失败：: io: read/write on closed pipe
2025-07-29 09:26:49 - InstagramDMTask - INFO - [模拟器3] 点击失败状态重置UI
2025-07-29 09:26:49 - InstagramDMTask - INFO - [模拟器3] 已点击失败状态，等待UI重置
2025-07-29 09:26:50 - InstagramDMTask - INFO - [模拟器3] 继续等待测试结果 (2/3)
2025-07-29 09:26:51 - InstagramDMTask - ERROR - [模拟器3] ❌ V2Ray节点延迟测试失败 (3/3): 失败：: io: read/write on closed pipe
2025-07-29 09:26:51 - InstagramDMTask - ERROR - [模拟器3] 达到最大失败次数 (3)，触发节点切换
2025-07-29 09:26:51 - InstagramDMTask - ERROR - [模拟器3] V2Ray节点延迟测试失败或超时
2025-07-29 09:26:51 - InstagramDMTask - ERROR - [模拟器3] V2Ray节点延迟测试失败，开始节点切换 (第1/8次)
2025-07-29 09:26:51 - InstagramDMTask - INFO - [模拟器3] 开始V2Ray节点切换流程
2025-07-29 09:26:51 - InstagramDMTask - INFO - [模拟器3] 开始智能滑动浏览节点列表
2025-07-29 09:26:52 - InstagramDMTask - INFO - [模拟器3] 计划执行 3 次随机滑动
2025-07-29 09:26:52 - InstagramDMTask - INFO - [模拟器3] 第 1/3 次滑动: down, 距离比例: 0.61
2025-07-29 09:26:53 - InstagramDMTask - INFO - [模拟器3] 第 1 次滑动失败
2025-07-29 09:26:53 - InstagramDMTask - INFO - [模拟器3] 滑动后等待 0.2 秒
2025-07-29 09:26:53 - InstagramDMTask - INFO - [模拟器3] 第 2/3 次滑动: down, 距离比例: 0.31
2025-07-29 09:26:54 - InstagramDMTask - INFO - [模拟器3] 第 2 次滑动失败
2025-07-29 09:26:54 - InstagramDMTask - INFO - [模拟器3] 滑动后等待 0.2 秒
2025-07-29 09:26:54 - InstagramDMTask - INFO - [模拟器3] 第 3/3 次滑动: down, 距离比例: 0.66
2025-07-29 09:26:54 - InstagramDMTask - INFO - [模拟器3] 第 3 次滑动失败
2025-07-29 09:26:54 - InstagramDMTask - INFO - [模拟器3] 滑动后等待 0.2 秒
2025-07-29 09:26:55 - InstagramDMTask - INFO - [模拟器3] ✅ 智能滑动完成，共执行 3 次
2025-07-29 09:26:55 - InstagramDMTask - INFO - [模拟器3] 开始随机选择V2Ray节点
2025-07-29 09:26:56 - InstagramDMTask - INFO - [模拟器3] 找到 5 个可用节点
2025-07-29 09:26:56 - InstagramDMTask - INFO - [模拟器3] 随机选择节点: V4-46|日本|x1.5
2025-07-29 09:26:56 - InstagramDMTask - INFO - [模拟器3] 节点位置: [11,227][82,241]
2025-07-29 09:26:56 - InstagramDMTask - INFO - [模拟器3] ✅ 成功点击节点: V4-46|日本|x1.5
2025-07-29 09:26:56 - InstagramDMTask - INFO - [模拟器3] 等待1秒让节点切换完成
2025-07-29 09:26:57 - InstagramDMTask - INFO - [模拟器3] 开始连接V2Ray节点
2025-07-29 09:26:58 - InstagramDMTask - INFO - [模拟器3] 当前连接状态: 已连接，点击测试连接
2025-07-29 09:26:58 - InstagramDMTask - INFO - [模拟器3] V2Ray节点已连接，无需重复连接
2025-07-29 09:26:58 - InstagramDMTask - INFO - [模拟器3] ✅ V2Ray节点切换成功
2025-07-29 09:26:58 - InstagramDMTask - INFO - [模拟器3] 节点切换成功，重新测试延迟
2025-07-29 09:26:58 - InstagramDMTask - INFO - [模拟器3] 开始测试V2Ray节点延迟
2025-07-29 09:26:58 - InstagramDMTask - INFO - [模拟器3] 开始V2Ray节点延迟测试
2025-07-29 09:26:59 - InstagramDMTask - INFO - [模拟器3] 当前测试状态: 已连接，点击测试连接
2025-07-29 09:26:59 - InstagramDMTask - INFO - [模拟器3] 点击开始延迟测试
2025-07-29 09:27:00 - InstagramDMTask - INFO - [模拟器3] 已点击测试按钮，等待测试结果
2025-07-29 09:27:02 - InstagramDMTask - INFO - [模拟器3] 测试状态监控 (1/30): 连接成功：延时 938 毫秒
2025-07-29 09:27:02 - InstagramDMTask - INFO - [模拟器3] ✅ V2Ray节点延迟测试成功: 连接成功：延时 938 毫秒
2025-07-29 09:27:02 - InstagramDMTask - INFO - [模拟器3] 等待5秒后进入下一阶段
2025-07-29 09:27:07 - InstagramDMTask - INFO - [模拟器3] 开始启动Instagram应用
2025-07-29 09:27:07 - InstagramDMTask - INFO - [模拟器3] Instagram启动命令执行成功，等待应用加载
2025-07-29 09:27:08 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-29 09:27:08 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-29 09:27:08 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-29 09:27:08 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-29 09:27:08 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-29 09:27:10 - InstagramDMTask - INFO - [模拟器3] ✅ Instagram应用启动命令执行完成
2025-07-29 09:27:10 - InstagramDMTask - INFO - [模拟器3] Instagram启动检测 第1/5次
2025-07-29 09:27:14 - InstagramDMTask - INFO - [模拟器3] ✅ 批量验证成功
2025-07-29 09:27:14 - InstagramDMTask - INFO - [模拟器3] ✅ 验证成功
2025-07-29 09:27:14 - InstagramFollowTask - INFO - [模拟器3] Instagram页面状态检测结果: 正常-在主页面
2025-07-29 09:27:14 - InstagramFollowTask - INFO - [模拟器3] ✅ Instagram已在主页面，可以继续执行任务
2025-07-29 09:27:14 - InstagramFollowTask - INFO - [模拟器3] 开始执行关注任务，模式: fans
2025-07-29 09:27:14 - InstagramFollowTask - INFO - [模拟器3] 开始执行【模式二：关注粉丝模式】
2025-07-29 09:27:14 - InstagramFollowTask - INFO - [模拟器3] 开始【模式二：关注粉丝循环】，目标关注数: 2
2025-07-29 09:27:14 - InstagramFollowTask - INFO - [模拟器3] 👥 开始处理目标用户: seiko_service (关注其粉丝)
2025-07-29 09:27:14 - InstagramFollowTask - INFO - [模拟器3] 开始处理用户：seiko_service
2025-07-29 09:27:14 - InstagramFollowTask - INFO - [模拟器3] 跳转到用户主页：seiko_service
2025-07-29 09:27:21 - InstagramFollowTask - INFO - [模拟器3] ✅ 检测到资料页元素，可能在用户资料页
2025-07-29 09:27:21 - InstagramFollowTask - INFO - [模拟器3] 当前已在用户主页：seiko_service
2025-07-29 09:27:28 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-29 09:27:28 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-29 09:27:28 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-29 09:27:28 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-29 09:27:28 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-29 09:27:30 - InstagramFollowTask - INFO - [模拟器3] 成功打开粉丝列表
2025-07-29 09:27:30 - InstagramFollowTask - INFO - [模拟器3] 开始检测粉丝列表
2025-07-29 09:27:31 - InstagramFollowTask - INFO - [模拟器3] 找到7个粉丝栏
2025-07-29 09:27:31 - InstagramFollowTask - INFO - [模拟器3] 成功提取 6 个粉丝信息
2025-07-29 09:27:31 - InstagramFollowTask - INFO - [模拟器3] 粉丝列表检测完成，耗时1.25秒
2025-07-29 09:27:31 - InstagramFollowTask - INFO - [模拟器3] 🔍 语言检测: 日文, 筛选结果: True
2025-07-29 09:27:31 - InstagramFollowTask - INFO - [模拟器3] 用户: 関西明装株式会社◎採用チーム 符合地区筛选
2025-07-29 09:27:31 - InstagramFollowTask - INFO - [模拟器3] 点击API返回值：，类型：<class 'str'>
2025-07-29 09:27:31 - InstagramFollowTask - INFO - [模拟器3] 关注成功，坐标：(215, 139)，API返回字符串：''
2025-07-29 09:27:32 - InstagramFollowTask - INFO - [模拟器3] 已关注数：1/2, 929毫秒后下一个, 耗时: 1.49 秒
2025-07-29 09:27:32 - InstagramFollowTask - INFO - [模拟器3] Instagram关注任务状态更新信号已发送: 关注中 (1/2)
2025-07-29 09:27:32 - MainWindowV2 - INFO - 模拟器3的Instagram任务状态已更新: 关注中 (1/2)
2025-07-29 09:27:32 - InstagramFollowTask - INFO - [模拟器3] 🔍 语言检测: 日文, 筛选结果: True
2025-07-29 09:27:32 - InstagramFollowTask - INFO - [模拟器3] 用户: 株式会社クリーンコーポレーション 符合地区筛选
2025-07-29 09:27:33 - InstagramFollowTask - INFO - [模拟器3] 点击API返回值：，类型：<class 'str'>
2025-07-29 09:27:33 - InstagramFollowTask - INFO - [模拟器3] 关注成功，坐标：(215, 229)，API返回字符串：''
2025-07-29 09:27:33 - InstagramFollowTask - INFO - [模拟器3] Instagram关注任务状态更新信号已发送: 已完成 (2/2)
2025-07-29 09:27:33 - MainWindowV2 - INFO - 模拟器3的Instagram任务状态已更新: 已完成 (2/2)
2025-07-29 09:27:33 - InstagramFollowTask - INFO - [模拟器3] 🎉 完成目标用户 seiko_service 的粉丝关注: 已完成任务，共关注 2 人
2025-07-29 09:27:33 - InstagramFollowTask - INFO - [模拟器3] ✅ 【模式二：关注粉丝循环】完成，成功关注: 2, 跳过蓝V: 0, 跳过私密: 0
2025-07-29 09:27:33 - InstagramDMTask - INFO - [模拟器3] Instagram任务配置观察者已注销
2025-07-29 09:27:33 - InstagramDMTask - INFO - [模拟器3] 开始任务完成后清理工作
2025-07-29 09:27:33 - TaskActivityHeartbeatManager - INFO - 模拟器 3 已从任务活动监控移除
2025-07-29 09:27:33 - InstagramDMTask - INFO - [模拟器3] 心跳监控已移除
2025-07-29 09:27:33 - InstagramDMTask - INFO - [模拟器3] 准备关闭模拟器
2025-07-29 09:27:33 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 3 | remaining_running: 0
2025-07-29 09:27:33 - Emulator - INFO - 模拟器停止成功 | emulator_id: 3
2025-07-29 09:27:33 - InstagramDMTask - INFO - [模拟器3] 模拟器已成功关闭
2025-07-29 09:27:33 - MainWindowV2 - INFO - 任务完成: 模拟器3, 任务stop
2025-07-29 09:27:33 - InstagramDMTask - INFO - [模拟器3] 任务完成后清理工作完成
2025-07-29 09:27:33 - MainWindowV2 - INFO - 模拟器3停止成功
2025-07-29 09:27:33 - InstagramFollowTaskThread - INFO - [模拟器3] Instagram关注粉丝任务执行成功
2025-07-29 09:27:33 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=3, 任务类型=stop, 结果={'result': '停止成功', 'message': ''}
2025-07-29 09:27:33 - WindowArrangementManager - INFO - 跳过非启动任务或失败任务: 任务类型=stop, 结果=停止成功
2025-07-29 09:27:33 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器4状态变化: 排队中 -> 启动中
2025-07-29 09:27:33 - Emulator - INFO - 模拟器状态变化 | emulator_id: 4 | old_state: 排队中 | new_state: 启动中
2025-07-29 09:27:33 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器4状态变化: 排队中 -> 启动中
2025-07-29 09:27:33 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 4
2025-07-29 09:27:33 - MainWindowV2 - INFO - 模拟器4: 排队中 -> 启动中
2025-07-29 09:27:33 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 1 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-29 09:27:33 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/1)
2025-07-29 09:27:33 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 4
2025-07-29 09:27:42 - InstagramDMUI - INFO - 刷新Instagram私信任务状态
2025-07-29 09:27:42 - InstagramDMUI - INFO - Instagram汇总统计: 关注0人, 私信0条 (异步计算中...)
2025-07-29 09:27:42 - InstagramSummaryService - INFO - 异步汇总完成: 关注0人, 私信0条 (耗时0.0ms)
2025-07-29 09:27:42 - InstagramDMUI - INFO - Instagram汇总统计更新: 关注0人, 私信0条
2025-07-29 09:27:43 - InstagramDMUI - INFO - 刷新Instagram私信任务状态
2025-07-29 09:27:43 - InstagramDMUI - INFO - Instagram汇总统计: 关注0人, 私信0条 (异步计算中...)
2025-07-29 09:27:43 - InstagramSummaryService - INFO - 异步汇总完成: 关注0人, 私信0条 (耗时0.0ms)
2025-07-29 09:27:43 - InstagramDMUI - INFO - Instagram汇总统计更新: 关注0人, 私信0条
2025-07-29 09:27:43 - Emulator - INFO - Android系统启动完成 | emulator_id: 4 | elapsed_time: 10.0秒
2025-07-29 09:27:43 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器4状态变化: 启动中 -> 运行中
2025-07-29 09:27:43 - Emulator - INFO - 模拟器状态变化 | emulator_id: 4 | old_state: 启动中 | new_state: 运行中
2025-07-29 09:27:43 - InstagramFollowTaskManager - INFO - 启动模拟器4的Instagram关注任务线程 - 当前并发: 2/1
2025-07-29 09:27:43 - TaskActivityHeartbeatManager - INFO - 模拟器 4 已添加到任务活动监控，失败计数: 0
2025-07-29 09:27:43 - Emulator - INFO - 模拟器启动成功 | emulator_id: 4 | running_count: 1
2025-07-29 09:27:43 - MainWindowV2 - INFO - 任务完成: 模拟器4, 任务start
2025-07-29 09:27:43 - MainWindowV2 - INFO - 模拟器4启动成功
2025-07-29 09:27:43 - InstagramTaskThread - INFO - [模拟器4] 开始等待启动完成
2025-07-29 09:27:43 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=4, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-29 09:27:43 - InstagramTaskThread - INFO - [模拟器4] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-29 09:27:43 - WindowArrangementManager - INFO - 模拟器4启动完成，立即触发窗口排列
2025-07-29 09:27:43 - InstagramTaskThread - INFO - [模拟器4] 开始窗口排列
2025-07-29 09:27:43 - MainWindowV2 - WARNING - 未找到模拟器4，无法更新状态
2025-07-29 09:27:43 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中
2025-07-29 09:27:43 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 1 | starting: 0 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 33.33333333333333 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-29 09:27:43 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中0个, 运行1个, 失败0个 (并发槽位:1/1)
2025-07-29 09:27:44 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中, PID: 24796
2025-07-29 09:27:44 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中
