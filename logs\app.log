2025-07-29 08:47:30 - root - INFO - 简化日志系统初始化完成
2025-07-29 08:47:30 - main - INFO - 应用程序启动
2025-07-29 08:47:30 - __main__ - INFO - Qt应用程序已创建
2025-07-29 08:47:30 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-29 08:47:30 - __main__ - INFO - 统一配置管理器已创建
2025-07-29 08:47:30 - DatabaseManager - INFO - SQLite连接已配置WAL模式
2025-07-29 08:47:30 - DatabaseManager - ERROR - 检查表结构失败: 1
2025-07-29 08:47:30 - DatabaseManager - INFO - 数据库表结构创建完成
2025-07-29 08:47:30 - DatabaseManager - INFO - 数据库初始化完成: E:\VScodexiangmu\insleidian2\emulator_system.db
2025-07-29 08:47:30 - EmulatorRepository - INFO - 模拟器数据仓库初始化完成
2025-07-29 08:47:30 - StartupManager - INFO - 统一启动管理器初始化完成
2025-07-29 08:47:30 - TaskActivityHeartbeatManager - INFO - 心跳检测已启用
2025-07-29 08:47:30 - TaskActivityHeartbeatManager - INFO - 任务活动检测心跳管理器初始化完成
2025-07-29 08:47:30 - TaskActivityHeartbeatManager - INFO - 心跳监控服务已启动，检测间隔: 20秒
2025-07-29 08:47:30 - UnifiedEmulatorManager - INFO - 统一模拟器管理器初始化完成
2025-07-29 08:47:30 - __main__ - INFO - 修复版异步桥梁已创建
2025-07-29 08:47:31 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-29 08:47:31 - BasicConfigUI - INFO - 基础配置信号连接完成
2025-07-29 08:47:31 - BasicConfigUI - INFO - 基础配置UI已注册为配置观察者，支持热加载
2025-07-29 08:47:31 - BasicConfigUI - INFO - 浏览按钮事件连接完成
2025-07-29 08:47:31 - BasicConfigUI - INFO - 基础配置UI初始化完成
2025-07-29 08:47:31 - InstagramDMUI - INFO - Instagram私信配置加载完成
2025-07-29 08:47:31 - InstagramFollowUI - INFO - Instagram关注配置加载完成
2025-07-29 08:47:31 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 程序启动)
2025-07-29 08:47:31 - MainWindowV2 - INFO - 事件驱动日志更新已启动 - 表格运行日志列实时更新（替代定时器）
2025-07-29 08:47:31 - MainWindowV2 - INFO - Instagram任务状态更新信号已连接
2025-07-29 08:47:31 - __main__ - INFO - UI主窗口已创建
2025-07-29 08:47:31 - MainWindowV2 - INFO - UI主窗口已创建
2025-07-29 08:47:31 - __main__ - INFO - 主窗口已显示
2025-07-29 08:47:31 - WindowArrangementManager - INFO - 窗口排列管理器初始化完成
2025-07-29 08:47:31 - FixedAsyncBridge - INFO - 任务完成信号已连接到窗口排列管理器
2025-07-29 08:47:31 - FixedAsyncBridge - INFO - 任务完成信号、状态变化信号、心跳监控信号和窗口排列信号已连接到UI
2025-07-29 08:47:31 - FixedAsyncBridge - INFO - 采用事件驱动方式，无需监控器
2025-07-29 08:47:31 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-29 08:47:31 - AsyncEventLoopManager - INFO - 专用异步事件循环已启动
2025-07-29 08:47:31 - __main__ - INFO - UI层和业务层已连接
2025-07-29 08:47:31 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-29 08:47:31 - __main__ - INFO - 启动Qt事件循环
2025-07-29 08:47:31 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-29 08:47:31 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-29 08:47:31 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-29 08:47:31 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 路径变化)
2025-07-29 08:47:31 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-29 08:47:31 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-29 08:47:31 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-29 08:47:31 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-29 08:47:31 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-29 08:47:31 - App - INFO - ldconsole命令执行成功，输出长度: 48374
2025-07-29 08:47:31 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-29 08:47:31 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-29 08:47:31 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-29 08:47:31 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-29 08:47:31 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.28s | count: 1229
2025-07-29 08:47:31 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-29 08:47:31 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-29 08:47:31 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-29 08:47:31 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-29 08:47:32 - App - INFO - ldconsole命令执行成功，输出长度: 48374
2025-07-29 08:47:32 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-29 08:47:32 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-29 08:47:32 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-29 08:47:32 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-29 08:47:32 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.31s | count: 1229
2025-07-29 08:47:32 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-29 08:47:32 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-29 08:47:32 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-29 08:47:32 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-29 08:47:32 - ConfigHotReloadService - INFO - 配置热加载服务初始化完成（Qt定时器模式）
2025-07-29 08:47:32 - ConfigHotReloadService - INFO - 配置热加载服务已启动（Qt定时器模式）
2025-07-29 08:47:32 - FixedAsyncBridge - INFO - 后台服务已启动
2025-07-29 08:47:32 - __main__ - INFO - 后台服务已启动
2025-07-29 08:47:32 - __main__ - INFO - 延迟启动服务完成
2025-07-29 08:47:37 - FixedAsyncBridge - INFO - 执行异步操作: instagram_follow_direct_task
2025-07-29 08:47:37 - MainWindowV2 - INFO - Instagram直接关注任务已启动，涉及3个模拟器
2025-07-29 08:47:37 - FixedAsyncBridge - INFO - 异步桥接器: 处理Instagram直接关注任务请求
2025-07-29 08:47:37 - MainWindowV2 - INFO - 用户启动Instagram直接关注任务，模拟器数量: 3
2025-07-29 08:47:37 - FixedAsyncBridge - INFO - 开始处理Instagram直接关注任务（线程池模式），模拟器数量: 3
2025-07-29 08:47:37 - FixedAsyncBridge - INFO - 使用批量启动功能启动模拟器: [3, 4, 5]
2025-07-29 08:47:37 - FixedAsyncBridge - INFO - 异步桥接器: 处理批量启动请求，模拟器数量: 3
2025-07-29 08:47:37 - StartupManager - INFO - 批量启动请求 | count: 3
2025-07-29 08:47:37 - StartupManager - INFO - 启动调度器已启动
2025-07-29 08:47:37 - FixedAsyncBridge - INFO - 异步桥接器: 批量启动请求已处理，状态: queued
2025-07-29 08:47:37 - FixedAsyncBridge - INFO - 模拟器批量启动成功，开始创建关注任务线程池
2025-07-29 08:47:37 - InstagramTaskManager - INFO - Instagram任务管理器已连接模拟器状态变化信号
2025-07-29 08:47:37 - InstagramTaskManager - INFO - Instagram任务管理器初始化完成，最大并发: 1
2025-07-29 08:47:37 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器初始化完成，模式: direct
2025-07-29 08:47:37 - InstagramTaskManager - INFO - 开始启动Instagram任务管理器，模拟器数量: 3
2025-07-29 08:47:37 - InstagramFollowTaskThread - INFO - [模拟器3] Instagram关注任务线程初始化完成，模式: direct
2025-07-29 08:47:37 - InstagramFollowTaskManager - INFO - 为模拟器 3 创建Instagram关注任务线程，模式: direct
2025-07-29 08:47:37 - InstagramFollowTaskThread - INFO - [模拟器4] Instagram关注任务线程初始化完成，模式: direct
2025-07-29 08:47:37 - InstagramFollowTaskManager - INFO - 为模拟器 4 创建Instagram关注任务线程，模式: direct
2025-07-29 08:47:37 - InstagramFollowTaskThread - INFO - [模拟器5] Instagram关注任务线程初始化完成，模式: direct
2025-07-29 08:47:37 - InstagramFollowTaskManager - INFO - 为模拟器 5 创建Instagram关注任务线程，模式: direct
2025-07-29 08:47:37 - InstagramTaskManager - INFO - 线程池创建完成: 成功创建3个Instagram线程
2025-07-29 08:47:37 - FixedAsyncBridge - INFO - 异步桥接器: Instagram直接关注任务请求已处理，状态: started
2025-07-29 08:47:37 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器3状态变化: 排队中 -> 启动中
2025-07-29 08:47:37 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器3状态变化: 排队中 -> 启动中
2025-07-29 08:47:37 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 3
2025-07-29 08:47:37 - MainWindowV2 - INFO - 3个模拟器: 未知 -> 排队中
2025-07-29 08:47:37 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 3 | starting: 0 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 0 | max_concurrent: 1
2025-07-29 08:47:37 - MainWindowV2 - INFO - 启动进度: 排队3个, 启动中0个, 运行0个, 失败0个 (并发槽位:0/1)
2025-07-29 08:47:37 - FixedAsyncBridge - INFO - 异步操作完成: start_batch
2025-07-29 08:47:37 - MainWindowV2 - INFO - 批量启动完成: 0/3 成功
2025-07-29 08:47:37 - MainWindowV2 - INFO - 批量启动操作完成 | success: 0 | total: 3
2025-07-29 08:47:37 - FixedAsyncBridge - INFO - 异步操作完成: instagram_follow_direct_task
2025-07-29 08:47:37 - Emulator - INFO - 模拟器状态变化 | emulator_id: 3 | old_state: 排队中 | new_state: 启动中
2025-07-29 08:47:37 - MainWindowV2 - INFO - 模拟器3: 排队中 -> 启动中
2025-07-29 08:47:37 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 2 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-29 08:47:37 - MainWindowV2 - INFO - 启动进度: 排队2个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/1)
2025-07-29 08:47:37 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 3
2025-07-29 08:47:47 - Emulator - INFO - Android系统启动完成 | emulator_id: 3 | elapsed_time: 10.1秒
2025-07-29 08:47:47 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器3状态变化: 启动中 -> 运行中
2025-07-29 08:47:47 - InstagramFollowTaskManager - INFO - 启动模拟器3的Instagram关注任务线程 - 当前并发: 1/1
2025-07-29 08:47:47 - Emulator - INFO - 模拟器状态变化 | emulator_id: 3 | old_state: 启动中 | new_state: 运行中
2025-07-29 08:47:47 - Emulator - INFO - 模拟器启动成功 | emulator_id: 3 | running_count: 1
2025-07-29 08:47:47 - TaskActivityHeartbeatManager - INFO - 模拟器 3 已添加到任务活动监控，失败计数: 0
2025-07-29 08:47:47 - MainWindowV2 - INFO - 任务完成: 模拟器3, 任务start
2025-07-29 08:47:47 - MainWindowV2 - INFO - 模拟器3启动成功
2025-07-29 08:47:47 - InstagramTaskThread - INFO - [模拟器3] 开始等待启动完成
2025-07-29 08:47:47 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=3, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-29 08:47:47 - InstagramTaskThread - INFO - [模拟器3] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-29 08:47:47 - InstagramTaskThread - INFO - [模拟器3] 开始窗口排列
2025-07-29 08:47:47 - WindowArrangementManager - INFO - 模拟器3启动完成，立即触发窗口排列
2025-07-29 08:47:47 - MainWindowV2 - WARNING - 未找到模拟器3，无法更新状态
2025-07-29 08:47:47 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中
2025-07-29 08:47:47 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 2 | starting: 0 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 33.33333333333333 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-29 08:47:47 - MainWindowV2 - INFO - 启动进度: 排队2个, 启动中0个, 运行1个, 失败0个 (并发槽位:1/1)
2025-07-29 08:47:48 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中, PID: 25684
2025-07-29 08:47:48 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中
2025-07-29 08:47:49 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-29 08:47:49 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-29 08:47:49 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-29 08:47:49 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-29 08:47:49 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-29 08:47:49 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-29 08:47:49 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-29 08:47:49 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-29 08:47:49 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-29 08:47:49 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-29 08:47:50 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-29 08:47:50 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-29 08:47:50 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-29 08:47:50 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-29 08:47:50 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-29 08:47:50 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-29 08:47:50 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-29 08:47:55 - InstagramTaskThread - INFO - [模拟器3] 窗口排列完成
2025-07-29 08:47:55 - InstagramFollowTaskThread - INFO - [模拟器3] 开始执行Instagram直接关注任务
2025-07-29 08:47:56 - InstagramDMTask - INFO - [模拟器3] 雷电模拟器API初始化成功
2025-07-29 08:47:56 - InstagramDMTask - INFO - [模拟器3] 模拟器路径: G:/leidian/LDPlayer9
2025-07-29 08:47:56 - InstagramDMTask - INFO - [模拟器3] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-29 08:47:56 - InstagramDMTask - INFO - [模拟器3] 已设置ld.emulator_id = 3
2025-07-29 08:47:56 - InstagramDMTask - INFO - [模拟器3] Instagram任务配置加载完成: 私信数量=2, 延迟=2-200ms
2025-07-29 08:47:56 - InstagramDMTask - INFO - [模拟器3] Instagram任务配置热加载观察者已注册
2025-07-29 08:47:56 - InstagramDMTask - INFO - [模拟器3] Instagram私信任务执行器初始化完成
2025-07-29 08:47:56 - InstagramFollowTask - INFO - [模拟器3] Instagram关注任务配置加载完成
2025-07-29 08:47:56 - InstagramFollowTask - INFO - [模拟器3] Instagram关注任务执行器初始化完成
2025-07-29 08:47:56 - InstagramFollowTask - INFO - [模拟器3] 关注模式已设置为: direct
2025-07-29 08:47:56 - InstagramFollowTask - INFO - [模拟器3] 开始执行Instagram关注任务
2025-07-29 08:47:56 - InstagramFollowTask - WARNING - [模拟器3] 任务开始时间未由线程传递，在此设置
2025-07-29 08:47:56 - InstagramFollowTask - INFO - [模拟器3] 任务超时设置: 666秒，已运行: 0.00秒
2025-07-29 08:47:56 - InstagramDMTask - INFO - [模拟器3] 模拟器Android系统运行正常，桌面稳定
2025-07-29 08:47:56 - InstagramDMTask - INFO - [模拟器3] 开始检查应用安装状态
2025-07-29 08:47:56 - InstagramDMTask - INFO - [模拟器3] ✅ V2Ray已安装，版本: 1.1.12
2025-07-29 08:47:56 - InstagramDMTask - INFO - [模拟器3] ✅ Instagram已安装，版本: 278.0.0.21.117
2025-07-29 08:47:56 - InstagramDMTask - INFO - [模拟器3] 📊 应用安装状态检测结果:
2025-07-29 08:47:56 - InstagramDMTask - INFO - [模拟器3] ✅ 所有必要应用已安装
2025-07-29 08:47:56 - InstagramDMTask - INFO - [模拟器3] 开始启动V2Ray应用
2025-07-29 08:47:56 - InstagramDMTask - INFO - [模拟器3] V2Ray启动命令执行成功，等待应用加载
2025-07-29 08:47:56 - InstagramDMTask - INFO - [模拟器3] V2Ray应用启动结果: 成功
2025-07-29 08:47:59 - InstagramDMTask - INFO - [模拟器3] ✅ V2Ray应用启动成功
2025-07-29 08:47:59 - InstagramDMTask - INFO - [模拟器3] 开始检查V2Ray节点列表状态
2025-07-29 08:48:00 - InstagramDMTask - INFO - [模拟器3] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-29 08:48:00 - InstagramDMTask - INFO - [模拟器3] 开始连接V2Ray节点
2025-07-29 08:48:02 - InstagramDMTask - INFO - [模拟器3] 当前连接状态: 未连接
2025-07-29 08:48:02 - InstagramDMTask - INFO - [模拟器3] V2Ray节点未连接，开始连接
2025-07-29 08:48:03 - InstagramDMTask - INFO - [模拟器3] 已点击连接按钮，等待连接完成
2025-07-29 08:48:05 - InstagramDMTask - INFO - [模拟器3] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-29 08:48:05 - InstagramDMTask - INFO - [模拟器3] V2Ray节点连接成功
2025-07-29 08:48:05 - InstagramDMTask - INFO - [模拟器3] 开始测试V2Ray节点延迟
2025-07-29 08:48:05 - InstagramDMTask - INFO - [模拟器3] 开始V2Ray节点延迟测试
2025-07-29 08:48:06 - InstagramDMTask - INFO - [模拟器3] 当前测试状态: 已连接，点击测试连接
2025-07-29 08:48:06 - InstagramDMTask - INFO - [模拟器3] 点击开始延迟测试
2025-07-29 08:48:07 - InstagramDMTask - INFO - [模拟器3] 已点击测试按钮，等待测试结果
2025-07-29 08:48:08 - InstagramDMTask - INFO - [模拟器3] 测试状态监控 (1/30): 连接成功：延时 225 毫秒
2025-07-29 08:48:08 - InstagramDMTask - INFO - [模拟器3] ✅ V2Ray节点延迟测试成功: 连接成功：延时 225 毫秒
2025-07-29 08:48:08 - InstagramDMTask - INFO - [模拟器3] 等待5秒后进入下一阶段
2025-07-29 08:48:10 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-29 08:48:10 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-29 08:48:10 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-29 08:48:10 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-29 08:48:10 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-29 08:48:13 - InstagramDMTask - INFO - [模拟器3] 开始启动Instagram应用
2025-07-29 08:48:13 - InstagramDMTask - INFO - [模拟器3] Instagram启动命令执行成功，等待应用加载
2025-07-29 08:48:16 - InstagramDMTask - INFO - [模拟器3] ✅ Instagram应用启动命令执行完成
2025-07-29 08:48:16 - InstagramDMTask - INFO - [模拟器3] Instagram启动检测 第1/5次
2025-07-29 08:48:21 - InstagramDMTask - INFO - [模拟器3] ✅ 批量验证成功
2025-07-29 08:48:21 - InstagramDMTask - INFO - [模拟器3] ✅ 验证成功
2025-07-29 08:48:21 - InstagramFollowTask - INFO - [模拟器3] Instagram页面状态检测结果: 正常-在主页面
2025-07-29 08:48:21 - InstagramFollowTask - INFO - [模拟器3] ✅ Instagram已在主页面，可以继续执行任务
2025-07-29 08:48:21 - InstagramFollowTask - INFO - [模拟器3] 开始执行关注任务，模式: direct
2025-07-29 08:48:21 - InstagramFollowTask - INFO - [模拟器3] 开始执行【模式一：直接关注模式】
2025-07-29 08:48:21 - InstagramFollowTask - INFO - [模拟器3] 开始【模式一：直接关注循环】，目标关注数: 5
2025-07-29 08:48:21 - InstagramFollowTask - INFO - [模拟器3] 开始处理目标用户: ririka_cool (已关注: 0/5)
2025-07-29 08:48:21 - InstagramFollowTask - INFO - [模拟器3] 开始处理用户：ririka_cool
2025-07-29 08:48:21 - InstagramFollowTask - INFO - [模拟器3] 跳转到用户主页：ririka_cool
2025-07-29 08:48:21 - InstagramFollowTask - INFO - [模拟器3] 开始检测用户信息
2025-07-29 08:48:25 - InstagramFollowTask - INFO - [模拟器3] 🔍 关注状态检测: 已关注
2025-07-29 08:48:25 - InstagramFollowTask - INFO - [模拟器3] ✅ 标题栏验证成功，当前在用户 ririka_cool 的资料页
2025-07-29 08:48:25 - InstagramFollowTask - INFO - [模拟器3] 用户信息检测完成，耗时3.99秒
2025-07-29 08:48:25 - InstagramFollowTask - INFO - [模拟器3] 该用户已关注,跳过
2025-07-29 08:48:25 - InstagramFollowTask - INFO - [模拟器3] ⏱️ 切换用户延迟: 22毫秒
2025-07-29 08:48:25 - InstagramFollowTask - INFO - [模拟器3] 开始处理目标用户: warakano_mac (已关注: 0/5)
2025-07-29 08:48:25 - InstagramFollowTask - INFO - [模拟器3] 开始处理用户：warakano_mac
2025-07-29 08:48:25 - InstagramFollowTask - INFO - [模拟器3] 跳转到用户主页：warakano_mac
2025-07-29 08:48:25 - InstagramFollowTask - INFO - [模拟器3] 开始检测用户信息
2025-07-29 08:48:28 - InstagramFollowTask - INFO - [模拟器3] 🔍 关注状态检测: 已关注
2025-07-29 08:48:28 - InstagramFollowTask - INFO - [模拟器3] ✅ 标题栏验证成功，当前在用户 warakano_mac 的资料页
2025-07-29 08:48:28 - InstagramFollowTask - INFO - [模拟器3] 用户信息检测完成，耗时3.41秒
2025-07-29 08:48:28 - InstagramFollowTask - INFO - [模拟器3] 该用户已关注,跳过
2025-07-29 08:48:28 - InstagramFollowTask - INFO - [模拟器3] ⏱️ 切换用户延迟: 7毫秒
2025-07-29 08:48:28 - InstagramFollowTask - INFO - [模拟器3] 开始处理目标用户: junpi_222 (已关注: 0/5)
2025-07-29 08:48:28 - InstagramFollowTask - INFO - [模拟器3] 开始处理用户：junpi_222
2025-07-29 08:48:29 - InstagramFollowTask - INFO - [模拟器3] 跳转到用户主页：junpi_222
2025-07-29 08:48:29 - InstagramFollowTask - INFO - [模拟器3] 开始检测用户信息
2025-07-29 08:48:30 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-29 08:48:30 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-29 08:48:30 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-29 08:48:30 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-29 08:48:30 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-29 08:48:32 - InstagramFollowTask - INFO - [模拟器3] 🔍 关注状态检测: 已关注
2025-07-29 08:48:32 - InstagramFollowTask - INFO - [模拟器3] ✅ 标题栏验证成功，当前在用户 junpi_222 的资料页
2025-07-29 08:48:32 - InstagramFollowTask - INFO - [模拟器3] 用户信息检测完成，耗时3.91秒
2025-07-29 08:48:32 - InstagramFollowTask - INFO - [模拟器3] 该用户已关注,跳过
2025-07-29 08:48:32 - InstagramFollowTask - INFO - [模拟器3] ⏱️ 切换用户延迟: 11毫秒
2025-07-29 08:48:32 - InstagramFollowTask - INFO - [模拟器3] 开始处理目标用户: _knu80 (已关注: 0/5)
2025-07-29 08:48:32 - InstagramFollowTask - INFO - [模拟器3] 开始处理用户：_knu80
2025-07-29 08:48:32 - InstagramFollowTask - INFO - [模拟器3] 跳转到用户主页：_knu80
2025-07-29 08:48:32 - InstagramFollowTask - INFO - [模拟器3] 开始检测用户信息
2025-07-29 08:48:36 - InstagramFollowTask - INFO - [模拟器3] 🔍 关注状态检测: 已关注
2025-07-29 08:48:36 - InstagramFollowTask - INFO - [模拟器3] ✅ 标题栏验证成功，当前在用户 _knu80 的资料页
2025-07-29 08:48:36 - InstagramFollowTask - INFO - [模拟器3] 用户信息检测完成，耗时3.66秒
2025-07-29 08:48:36 - InstagramFollowTask - INFO - [模拟器3] 该用户已关注,跳过
2025-07-29 08:48:36 - InstagramFollowTask - INFO - [模拟器3] ⏱️ 切换用户延迟: 16毫秒
2025-07-29 08:48:36 - InstagramFollowTask - INFO - [模拟器3] 开始处理目标用户: _11shk4_ (已关注: 0/5)
2025-07-29 08:48:36 - InstagramFollowTask - INFO - [模拟器3] 开始处理用户：_11shk4_
2025-07-29 08:48:36 - InstagramFollowTask - INFO - [模拟器3] 跳转到用户主页：_11shk4_
2025-07-29 08:48:36 - InstagramFollowTask - INFO - [模拟器3] 开始检测用户信息
2025-07-29 08:48:41 - InstagramFollowTask - INFO - [模拟器3] 🔍 关注状态检测: 已关注
2025-07-29 08:48:41 - InstagramFollowTask - INFO - [模拟器3] ✅ 标题栏验证成功，当前在用户 _11shk4_ 的资料页
2025-07-29 08:48:41 - InstagramFollowTask - INFO - [模拟器3] 用户信息检测完成，耗时4.40秒
2025-07-29 08:48:41 - InstagramFollowTask - INFO - [模拟器3] 该用户已关注,跳过
2025-07-29 08:48:41 - InstagramFollowTask - INFO - [模拟器3] ⏱️ 切换用户延迟: 15毫秒
2025-07-29 08:48:41 - InstagramFollowTask - INFO - [模拟器3] 开始处理目标用户: yuuuna_22_ig (已关注: 0/5)
2025-07-29 08:48:41 - InstagramFollowTask - INFO - [模拟器3] 开始处理用户：yuuuna_22_ig
2025-07-29 08:48:41 - InstagramFollowTask - INFO - [模拟器3] 跳转到用户主页：yuuuna_22_ig
2025-07-29 08:48:41 - InstagramFollowTask - INFO - [模拟器3] 开始检测用户信息
2025-07-29 08:48:45 - InstagramFollowTask - INFO - [模拟器3] 🔍 关注状态检测: 关注
2025-07-29 08:48:45 - InstagramFollowTask - INFO - [模拟器3] ✅ 标题栏验证成功，当前在用户 yuuuna_22_ig 的资料页
2025-07-29 08:48:45 - InstagramFollowTask - INFO - [模拟器3] 用户信息检测完成，耗时4.23秒
2025-07-29 08:48:45 - InstagramFollowTask - INFO - [模拟器3] 该用户未关注,开始关注
2025-07-29 08:48:47 - InstagramFollowTask - INFO - [模拟器3] 关注完成,已关注 1 / 5
2025-07-29 08:48:47 - InstagramFollowTask - INFO - [模拟器3] 直接关注任务状态更新信号已发送: 关注中 (1/5)
2025-07-29 08:48:47 - InstagramFollowTask - INFO - [模拟器3] ⏱️ 切换用户延迟: 18毫秒
2025-07-29 08:48:47 - MainWindowV2 - INFO - 模拟器3的Instagram任务状态已更新: 关注中 (1/5)
2025-07-29 08:48:47 - InstagramFollowTask - INFO - [模拟器3] 开始处理目标用户: fuka_9_ (已关注: 1/5)
2025-07-29 08:48:47 - InstagramFollowTask - INFO - [模拟器3] 开始处理用户：fuka_9_
2025-07-29 08:48:48 - InstagramFollowTask - INFO - [模拟器3] 跳转到用户主页：fuka_9_
2025-07-29 08:48:48 - InstagramFollowTask - INFO - [模拟器3] 开始检测用户信息
2025-07-29 08:48:50 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-29 08:48:50 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-29 08:48:50 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-29 08:48:50 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-29 08:48:50 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-29 08:48:52 - InstagramFollowTask - INFO - [模拟器3] 🔍 关注状态检测: 关注
2025-07-29 08:48:52 - InstagramFollowTask - INFO - [模拟器3] ✅ 标题栏验证成功，当前在用户 fuka_9_ 的资料页
2025-07-29 08:48:52 - InstagramFollowTask - INFO - [模拟器3] 用户信息检测完成，耗时4.27秒
2025-07-29 08:48:52 - InstagramFollowTask - INFO - [模拟器3] 该用户未关注,开始关注
2025-07-29 08:48:54 - InstagramFollowTask - INFO - [模拟器3] 关注完成,已关注 2 / 5
2025-07-29 08:48:54 - InstagramFollowTask - INFO - [模拟器3] 直接关注任务状态更新信号已发送: 关注中 (2/5)
2025-07-29 08:48:54 - MainWindowV2 - INFO - 模拟器3的Instagram任务状态已更新: 关注中 (2/5)
2025-07-29 08:48:54 - InstagramFollowTask - INFO - [模拟器3] ⏱️ 切换用户延迟: 11毫秒
2025-07-29 08:48:54 - InstagramFollowTask - INFO - [模拟器3] 开始处理目标用户: hiltontokyobay (已关注: 2/5)
2025-07-29 08:48:54 - InstagramFollowTask - INFO - [模拟器3] 开始处理用户：hiltontokyobay
2025-07-29 08:48:54 - InstagramFollowTask - INFO - [模拟器3] 跳转到用户主页：hiltontokyobay
2025-07-29 08:48:54 - InstagramFollowTask - INFO - [模拟器3] 开始检测用户信息
2025-07-29 08:48:58 - InstagramFollowTask - INFO - [模拟器3] 🔍 关注状态检测: 关注
2025-07-29 08:48:58 - InstagramFollowTask - INFO - [模拟器3] ✅ 标题栏验证成功，当前在用户 hiltontokyobay 的资料页
2025-07-29 08:48:58 - InstagramFollowTask - INFO - [模拟器3] 用户信息检测完成，耗时4.18秒
2025-07-29 08:48:58 - InstagramFollowTask - INFO - [模拟器3] 该用户未关注,开始关注
2025-07-29 08:49:00 - InstagramFollowTask - INFO - [模拟器3] 关注完成,已关注 3 / 5
2025-07-29 08:49:00 - InstagramFollowTask - INFO - [模拟器3] 直接关注任务状态更新信号已发送: 关注中 (3/5)
2025-07-29 08:49:00 - MainWindowV2 - INFO - 模拟器3的Instagram任务状态已更新: 关注中 (3/5)
2025-07-29 08:49:00 - InstagramFollowTask - INFO - [模拟器3] ⏱️ 切换用户延迟: 6毫秒
2025-07-29 08:49:00 - InstagramFollowTask - INFO - [模拟器3] 开始处理目标用户: fuchan.728 (已关注: 3/5)
2025-07-29 08:49:00 - InstagramFollowTask - INFO - [模拟器3] 开始处理用户：fuchan.728
2025-07-29 08:49:00 - InstagramFollowTask - INFO - [模拟器3] 跳转到用户主页：fuchan.728
2025-07-29 08:49:00 - InstagramFollowTask - INFO - [模拟器3] 开始检测用户信息
2025-07-29 08:49:04 - InstagramFollowTask - INFO - [模拟器3] 🔍 关注状态检测: 关注
2025-07-29 08:49:04 - InstagramFollowTask - INFO - [模拟器3] ✅ 标题栏验证成功，当前在用户 fuchan.728 的资料页
2025-07-29 08:49:04 - InstagramFollowTask - INFO - [模拟器3] 用户信息检测完成，耗时4.62秒
2025-07-29 08:49:04 - InstagramFollowTask - INFO - [模拟器3] 该用户未关注,开始关注
2025-07-29 08:49:06 - InstagramFollowTask - INFO - [模拟器3] 关注完成,已关注 4 / 5
2025-07-29 08:49:06 - InstagramFollowTask - INFO - [模拟器3] 直接关注任务状态更新信号已发送: 关注中 (4/5)
2025-07-29 08:49:06 - InstagramFollowTask - INFO - [模拟器3] ⏱️ 切换用户延迟: 8毫秒
2025-07-29 08:49:06 - MainWindowV2 - INFO - 模拟器3的Instagram任务状态已更新: 关注中 (4/5)
2025-07-29 08:49:06 - InstagramFollowTask - INFO - [模拟器3] 开始处理目标用户: babiron_achubu (已关注: 4/5)
2025-07-29 08:49:06 - InstagramFollowTask - INFO - [模拟器3] 开始处理用户：babiron_achubu
2025-07-29 08:49:06 - InstagramFollowTask - INFO - [模拟器3] 跳转到用户主页：babiron_achubu
2025-07-29 08:49:06 - InstagramFollowTask - INFO - [模拟器3] 开始检测用户信息
2025-07-29 08:49:10 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-29 08:49:10 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-29 08:49:10 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-29 08:49:10 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-29 08:49:10 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-29 08:49:11 - InstagramFollowTask - INFO - [模拟器3] 🔍 关注状态检测: 关注
2025-07-29 08:49:11 - InstagramFollowTask - INFO - [模拟器3] ✅ 标题栏验证成功，当前在用户 babiron_achubu 的资料页
2025-07-29 08:49:11 - InstagramFollowTask - INFO - [模拟器3] 用户信息检测完成，耗时4.63秒
2025-07-29 08:49:11 - InstagramFollowTask - INFO - [模拟器3] 该用户未关注,开始关注
2025-07-29 08:49:12 - InstagramFollowTask - INFO - [模拟器3] 关注完成,已关注 5 / 5
2025-07-29 08:49:12 - InstagramFollowTask - INFO - [模拟器3] 直接关注任务状态更新信号已发送: 已完成 (5/5)
2025-07-29 08:49:12 - MainWindowV2 - INFO - 模拟器3的Instagram任务状态已更新: 已完成 (5/5)
2025-07-29 08:49:12 - InstagramFollowTask - INFO - [模拟器3] 任务完成,退出循环.--任务进度 :5 / 5 耗时: 51.52秒
2025-07-29 08:49:12 - InstagramFollowTask - INFO - [模拟器3] 【模式一：直接关注循环】完成，成功关注: 5, 跳过蓝V: 0, 跳过私密: 0
2025-07-29 08:49:12 - InstagramDMTask - INFO - [模拟器3] Instagram任务配置观察者已注销
2025-07-29 08:49:12 - InstagramDMTask - INFO - [模拟器3] 开始任务完成后清理工作
2025-07-29 08:49:12 - TaskActivityHeartbeatManager - INFO - 模拟器 3 已从任务活动监控移除
2025-07-29 08:49:12 - InstagramDMTask - INFO - [模拟器3] 心跳监控已移除
2025-07-29 08:49:12 - InstagramDMTask - INFO - [模拟器3] 准备关闭模拟器
2025-07-29 08:49:12 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 3 | remaining_running: 0
2025-07-29 08:49:12 - Emulator - INFO - 模拟器停止成功 | emulator_id: 3
2025-07-29 08:49:12 - InstagramDMTask - INFO - [模拟器3] 模拟器已成功关闭
2025-07-29 08:49:12 - InstagramDMTask - INFO - [模拟器3] 任务完成后清理工作完成
2025-07-29 08:49:12 - InstagramFollowTaskThread - INFO - [模拟器3] Instagram直接关注任务执行成功
2025-07-29 08:49:12 - MainWindowV2 - INFO - 任务完成: 模拟器3, 任务stop
2025-07-29 08:49:12 - MainWindowV2 - INFO - 模拟器3停止成功
2025-07-29 08:49:12 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=3, 任务类型=stop, 结果={'result': '停止成功', 'message': ''}
2025-07-29 08:49:12 - WindowArrangementManager - INFO - 跳过非启动任务或失败任务: 任务类型=stop, 结果=停止成功
2025-07-29 08:49:13 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器4状态变化: 排队中 -> 启动中
2025-07-29 08:49:13 - Emulator - INFO - 模拟器状态变化 | emulator_id: 4 | old_state: 排队中 | new_state: 启动中
2025-07-29 08:49:13 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器4状态变化: 排队中 -> 启动中
2025-07-29 08:49:13 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 4
2025-07-29 08:49:13 - MainWindowV2 - INFO - 模拟器4: 排队中 -> 启动中
2025-07-29 08:49:13 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 1 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-29 08:49:13 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/1)
2025-07-29 08:49:13 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 4
2025-07-29 08:49:27 - Emulator - INFO - Android系统启动完成 | emulator_id: 4 | elapsed_time: 13.1秒
2025-07-29 08:49:27 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器4状态变化: 启动中 -> 运行中
2025-07-29 08:49:27 - InstagramFollowTaskManager - INFO - 启动模拟器4的Instagram关注任务线程 - 当前并发: 2/1
2025-07-29 08:49:27 - Emulator - INFO - 模拟器状态变化 | emulator_id: 4 | old_state: 启动中 | new_state: 运行中
2025-07-29 08:49:27 - Emulator - INFO - 模拟器启动成功 | emulator_id: 4 | running_count: 1
2025-07-29 08:49:27 - TaskActivityHeartbeatManager - INFO - 模拟器 4 已添加到任务活动监控，失败计数: 0
2025-07-29 08:49:27 - InstagramTaskThread - INFO - [模拟器4] 开始等待启动完成
2025-07-29 08:49:27 - MainWindowV2 - INFO - 任务完成: 模拟器4, 任务start
2025-07-29 08:49:27 - InstagramTaskThread - INFO - [模拟器4] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-29 08:49:27 - MainWindowV2 - INFO - 模拟器4启动成功
2025-07-29 08:49:27 - InstagramTaskThread - INFO - [模拟器4] 开始窗口排列
2025-07-29 08:49:27 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=4, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-29 08:49:27 - WindowArrangementManager - INFO - 模拟器4启动完成，立即触发窗口排列
2025-07-29 08:49:27 - MainWindowV2 - WARNING - 未找到模拟器4，无法更新状态
2025-07-29 08:49:27 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中
2025-07-29 08:49:27 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 1 | starting: 0 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 33.33333333333333 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-29 08:49:27 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中0个, 运行1个, 失败0个 (并发槽位:1/1)
2025-07-29 08:49:27 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中, PID: 27376
2025-07-29 08:49:27 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中
2025-07-29 08:49:29 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-29 08:49:29 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-29 08:49:29 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-29 08:49:29 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-29 08:49:29 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-29 08:49:29 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-29 08:49:29 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-4' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-29 08:49:29 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-29 08:49:29 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-29 08:49:29 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-4' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-29 08:49:29 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-29 08:49:29 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
