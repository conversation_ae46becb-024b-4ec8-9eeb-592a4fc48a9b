"""
Instagram汇总统计服务 - 事件驱动增量版本
功能：基于事件驱动的实时统计，支持增量更新和异步处理
"""

import re
import logging
import time
from typing import Tuple
from PyQt6.QtCore import QObject, pyqtSignal, QThread


class AsyncSummaryWorker(QThread):
    """异步汇总统计工作线程"""

    # 结果信号
    result_ready = pyqtSignal(int, int, float)  # follow_count, dm_count, process_time

    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(self.__class__.__name__)

        # 预编译正则表达式
        self._follow_pattern = re.compile(r'关注.*[已完成关注中].*\((\d+)/\d+\)')
        self._dm_pattern = re.compile(r'[（(](?:成功|已发送)(\d+)/(?:目标)?(\d+)[）)]')

        # 工作数据
        self._emulator_data = []
        self._should_process = False

    def process_data(self, emulator_data):
        """提交数据处理请求"""
        self._emulator_data = emulator_data.copy()
        self._should_process = True

        if not self.isRunning():
            self.start()

    def run(self):
        """异步处理主循环"""
        try:
            if not self._should_process:
                return

            start_time = time.time()
            follow_count = dm_count = 0

            # 1. 统计当前活跃模拟器
            for row in self._emulator_data:
                if len(row) > 8 and row[8] and row[8] != "-":
                    status = row[8]

                    # 关注解析
                    follow_match = self._follow_pattern.search(status)
                    if follow_match:
                        follow_count += int(follow_match.group(1))

                    # 私信解析
                    dm_match = self._dm_pattern.search(status)
                    if dm_match:
                        dm_count += int(dm_match.group(1))

            # 异步处理只统计当前活跃模拟器

            process_time = (time.time() - start_time) * 1000
            self.result_ready.emit(follow_count, dm_count, process_time)

        except Exception as e:
            self.logger.error(f"异步处理失败: {e}")
            self.result_ready.emit(0, 0, 0)


class InstagramSummaryService(QObject):
    """Instagram汇总统计服务 - 事件驱动增量版本"""

    # 结果更新信号
    summary_updated = pyqtSignal(int, int)  # follow_count, dm_count

    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(self.__class__.__name__)

        # 移除已完成任务数据，完全依赖事件驱动的实时统计

        # 异步工作线程
        self._worker = AsyncSummaryWorker()
        self._worker.result_ready.connect(self._on_result_ready)

        # 预编译正则表达式（用于同步快速返回）
        self._follow_pattern = re.compile(r'关注.*[已完成关注中].*\((\d+)/\d+\)')
        self._dm_pattern = re.compile(r'[（(](?:成功|已发送)(\d+)/(?:目标)?(\d+)[）)]')

        # 添加调试日志
        self.logger.info("Instagram汇总统计服务已初始化")

    def get_summary(self, emulator_data, force_refresh=True) -> Tuple[int, int]:
        """获取汇总统计 - 立即返回缓存值，异步更新"""
        try:
            # 1. 立即返回当前缓存值（快速响应）
            current_follow = 0
            current_dm = 0

            # 快速统计当前活跃模拟器
            for row in emulator_data:
                if len(row) > 8 and row[8] and row[8] != "-":
                    status = row[8]

                    # 添加调试日志
                    self.logger.debug(f"处理模拟器状态: {status}")

                    # 关注解析
                    follow_match = self._follow_pattern.search(status)
                    if follow_match:
                        count = int(follow_match.group(1))
                        current_follow += count
                        self.logger.debug(f"匹配到关注数: {count}")

                    # 私信解析
                    dm_match = self._dm_pattern.search(status)
                    if dm_match:
                        count = int(dm_match.group(1))
                        current_dm += count
                        self.logger.debug(f"匹配到私信数: {count}")

            # 2. 如果需要强制刷新，启动异步处理
            if force_refresh:
                self._worker.process_data(emulator_data)

            self.logger.info(f"汇总统计结果: 关注{current_follow}人, 私信{current_dm}条")
            return current_follow, current_dm

        except Exception as e:
            self.logger.error(f"汇总统计失败: {e}")
            return 0, 0

    def _on_result_ready(self, follow_count: int, dm_count: int, process_time: float):
        """处理异步结果"""
        try:
            self.logger.info(f"异步汇总完成: 关注{follow_count}人, 私信{dm_count}条 (耗时{process_time:.1f}ms)")
            self.summary_updated.emit(follow_count, dm_count)
        except Exception as e:
            self.logger.error(f"处理异步结果失败: {e}")

    def reset_data(self):
        """重置所有数据（事件驱动模式下无需重置）"""
        self.logger.info("事件驱动模式：数据自动更新，无需手动重置")
        self.summary_updated.emit(0, 0)