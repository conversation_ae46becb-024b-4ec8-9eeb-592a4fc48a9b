"""
Instagram汇总统计服务 - 异步+持久化版本
功能：统计所有模拟器的关注人数和私信人数，支持数据持久化和异步处理
"""

import re
import logging
import json
import os
import time
from typing import Tuple, Dict
from PyQt6.QtCore import QObject, pyqtSignal, QThread


class AsyncSummaryWorker(QThread):
    """异步汇总统计工作线程"""

    # 结果信号
    result_ready = pyqtSignal(int, int, float)  # follow_count, dm_count, process_time

    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(self.__class__.__name__)

        # 预编译正则表达式
        self._follow_pattern = re.compile(r'关注.*[已完成关注中].*\((\d+)/\d+\)')
        self._dm_pattern = re.compile(r'[（(](?:成功|已发送)(\d+)/(?:目标)?(\d+)[）)]')

        # 工作数据
        self._emulator_data = []
        self._persistent_data = {}
        self._should_process = False

    def process_data(self, emulator_data, persistent_data):
        """提交数据处理请求"""
        self._emulator_data = emulator_data.copy()
        self._persistent_data = persistent_data.copy()
        self._should_process = True

        if not self.isRunning():
            self.start()

    def run(self):
        """异步处理主循环"""
        try:
            if not self._should_process:
                return

            start_time = time.time()
            follow_count = dm_count = 0

            # 1. 统计当前活跃模拟器
            for row in self._emulator_data:
                if len(row) > 8 and row[8] and row[8] != "-":
                    status = row[8]

                    # 关注解析
                    follow_match = self._follow_pattern.search(status)
                    if follow_match:
                        follow_count += int(follow_match.group(1))

                    # 私信解析
                    dm_match = self._dm_pattern.search(status)
                    if dm_match:
                        dm_count += int(dm_match.group(1))

            # 2. 加上持久化的历史数据
            follow_count += self._persistent_data.get('total_follow', 0)
            dm_count += self._persistent_data.get('total_dm', 0)

            process_time = (time.time() - start_time) * 1000
            self.result_ready.emit(follow_count, dm_count, process_time)

        except Exception as e:
            self.logger.error(f"异步处理失败: {e}")
            self.result_ready.emit(0, 0, 0)


class InstagramSummaryService(QObject):
    """Instagram汇总统计服务 - 异步+持久化版本"""

    # 结果更新信号
    summary_updated = pyqtSignal(int, int)  # follow_count, dm_count

    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(self.__class__.__name__)

        # 数据持久化文件
        self._data_file = "data/instagram_summary.json"
        self._ensure_data_dir()

        # 持久化数据
        self._persistent_data = self._load_persistent_data()

        # 异步工作线程
        self._worker = AsyncSummaryWorker()
        self._worker.result_ready.connect(self._on_result_ready)

        # 预编译正则表达式（用于同步快速返回）
        self._follow_pattern = re.compile(r'关注.*[已完成关注中].*\((\d+)/\d+\)')
        self._dm_pattern = re.compile(r'[（(](?:成功|已发送)(\d+)/(?:目标)?(\d+)[）)]')

    def get_summary(self, emulator_data, force_refresh=True) -> Tuple[int, int]:
        """获取汇总统计 - 立即返回缓存值，异步更新"""
        try:
            # 1. 立即返回当前缓存值（快速响应）
            current_follow = 0
            current_dm = 0

            # 快速统计当前活跃模拟器
            for row in emulator_data:
                if len(row) > 8 and row[8] and row[8] != "-":
                    status = row[8]

                    # 关注解析
                    follow_match = self._follow_pattern.search(status)
                    if follow_match:
                        current_follow += int(follow_match.group(1))

                    # 私信解析
                    dm_match = self._dm_pattern.search(status)
                    if dm_match:
                        current_dm += int(dm_match.group(1))

            # 加上持久化数据
            total_follow = current_follow + self._persistent_data.get('total_follow', 0)
            total_dm = current_dm + self._persistent_data.get('total_dm', 0)

            # 2. 如果需要强制刷新，启动异步处理
            if force_refresh:
                self._worker.process_data(emulator_data, self._persistent_data)

            return total_follow, total_dm

        except Exception as e:
            self.logger.error(f"汇总统计失败: {e}")
            return 0, 0

    def _on_result_ready(self, follow_count: int, dm_count: int, process_time: float):
        """处理异步结果"""
        try:
            self.logger.info(f"异步汇总完成: 关注{follow_count}人, 私信{dm_count}条 (耗时{process_time:.1f}ms)")
            self.summary_updated.emit(follow_count, dm_count)
        except Exception as e:
            self.logger.error(f"处理异步结果失败: {e}")

    def save_completed_task(self, emulator_id: int, follow_count: int, dm_count: int):
        """保存已完成任务的数据"""
        try:
            # 更新持久化数据
            self._persistent_data['total_follow'] = self._persistent_data.get('total_follow', 0) + follow_count
            self._persistent_data['total_dm'] = self._persistent_data.get('total_dm', 0) + dm_count
            self._persistent_data['last_updated'] = time.time()

            # 保存到文件
            self._save_persistent_data()

            self.logger.info(f"模拟器{emulator_id}完成数据已保存: 关注+{follow_count}, 私信+{dm_count}")

        except Exception as e:
            self.logger.error(f"保存完成任务数据失败: {e}")

    def _ensure_data_dir(self):
        """确保数据目录存在"""
        try:
            data_dir = os.path.dirname(self._data_file)
            if data_dir and not os.path.exists(data_dir):
                os.makedirs(data_dir)
        except Exception as e:
            self.logger.error(f"创建数据目录失败: {e}")

    def _load_persistent_data(self) -> dict:
        """加载持久化数据"""
        try:
            if os.path.exists(self._data_file):
                with open(self._data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.logger.info(f"加载持久化数据: 关注{data.get('total_follow', 0)}人, 私信{data.get('total_dm', 0)}条")
                    return data
        except Exception as e:
            self.logger.error(f"加载持久化数据失败: {e}")

        return {'total_follow': 0, 'total_dm': 0, 'last_updated': time.time()}

    def _save_persistent_data(self):
        """保存持久化数据"""
        try:
            with open(self._data_file, 'w', encoding='utf-8') as f:
                json.dump(self._persistent_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存持久化数据失败: {e}")

    def reset_data(self):
        """重置所有数据"""
        try:
            self._persistent_data = {'total_follow': 0, 'total_dm': 0, 'last_updated': time.time()}
            self._save_persistent_data()
            self.logger.info("汇总数据已重置")
        except Exception as e:
            self.logger.error(f"重置数据失败: {e}")