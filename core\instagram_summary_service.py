"""
Instagram汇总统计服务 - 极简版本
功能：统计所有模拟器的关注人数和私信人数
"""

import re
import logging
from typing import Tuple


class InstagramSummaryService:
    """Instagram汇总统计服务 - 极简版本"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 预编译正则表达式
        self._follow_pattern = re.compile(r'关注.*[已完成关注中].*\((\d+)/\d+\)')
        self._dm_pattern = re.compile(r'[（(](?:成功|已发送)(\d+)/(?:目标)?(\d+)[）)]')
    
    def get_summary(self, emulator_data) -> Tuple[int, int]:
        """
        获取汇总统计
        
        Args:
            emulator_data: 模拟器数据列表
            
        Returns:
            (关注人数, 私信人数)
        """
        try:
            if not emulator_data:
                return 0, 0
            
            total_follow_count = 0
            total_dm_count = 0
            
            # 遍历所有模拟器数据
            for row in emulator_data:
                if len(row) > 8:  # 确保有任务状态列
                    task_status = row[8]
                    if task_status and task_status != "-":
                        # 解析关注数
                        follow_match = self._follow_pattern.search(task_status)
                        if follow_match:
                            total_follow_count += int(follow_match.group(1))
                        
                        # 解析私信数
                        dm_match = self._dm_pattern.search(task_status)
                        if dm_match:
                            total_dm_count += int(dm_match.group(1))
            
            return total_follow_count, total_dm_count
            
        except Exception as e:
            self.logger.error(f"汇总统计计算失败: {e}")
            return 0, 0


def create_summary_service():
    """创建汇总统计服务实例"""
    return InstagramSummaryService()
