"""
Instagram汇总统计服务 - 高性能版本
提供缓存、增量更新、异步处理等优化功能
"""

import time
import hashlib
import re
import logging
from typing import Dict, Optional, Tuple
from PyQt6.QtCore import QObject, pyqtSignal, QTimer


class InstagramSummaryService(QObject):
    """Instagram汇总统计服务 - 单例模式，避免重复计算"""
    
    # 信号：统计数据更新
    summary_updated = pyqtSignal(int, int)  # follow_count, dm_count
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        super().__init__()
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 🎯 高性能缓存系统
        self._cache = {
            'total_follow_count': 0,
            'total_dm_count': 0,
            'last_update_time': 0,
            'data_hash': None,
            'processed_count': 0
        }
        
        # 🎯 性能配置
        self._cache_ttl = 3.0  # 缓存有效期3秒
        self._batch_size = 100  # 批处理大小
        
        # 🎯 预编译正则表达式，提高性能
        self._follow_pattern = re.compile(r'关注.*[已完成关注中].*\((\d+)/\d+\)')
        self._dm_pattern = re.compile(r'[（(](?:成功|已发送)(\d+)/(?:目标)?(\d+)[）)]')
        
        # 🎯 防抖定时器，避免频繁更新
        self._debounce_timer = QTimer()
        self._debounce_timer.setSingleShot(True)
        self._debounce_timer.timeout.connect(self._emit_update_signal)
        self._pending_update = None
        
        self._initialized = True
        self.logger.info("Instagram汇总统计服务初始化完成")
    
    def get_summary(self, table_model=None, force_refresh=False) -> Tuple[int, int]:
        """获取汇总统计 - 高性能版本"""
        try:
            current_time = time.time()
            
            # 🎯 缓存检查
            if not force_refresh and current_time - self._cache['last_update_time'] < self._cache_ttl:
                return self._cache['total_follow_count'], self._cache['total_dm_count']
            
            if not table_model or not hasattr(table_model, 'emulators'):
                return 0, 0
            
            # 🎯 数据变化检测
            data_hash = self._calculate_data_hash(table_model.emulators)
            if not force_refresh and data_hash == self._cache['data_hash']:
                self._cache['last_update_time'] = current_time
                return self._cache['total_follow_count'], self._cache['total_dm_count']
            
            # 🎯 高性能批量解析
            follow_count, dm_count, processed = self._parse_emulator_data(table_model.emulators)
            
            # 🎯 更新缓存
            self._cache.update({
                'total_follow_count': follow_count,
                'total_dm_count': dm_count,
                'last_update_time': current_time,
                'data_hash': data_hash,
                'processed_count': processed
            })
            
            # 🎯 防抖信号发送
            self._schedule_update_signal(follow_count, dm_count)
            
            self.logger.debug(f"汇总统计更新: 处理{processed}/{len(table_model.emulators)}行, "
                            f"关注{follow_count}人, 私信{dm_count}条")
            
            return follow_count, dm_count
            
        except Exception as e:
            self.logger.error(f"获取汇总统计失败: {e}")
            return 0, 0
    
    def _calculate_data_hash(self, emulators) -> str:
        """计算数据哈希值 - 只针对任务状态列"""
        try:
            status_data = []
            for row in emulators:
                if len(row) > 8:
                    status_data.append(row[8] or "-")
            
            return hashlib.md5('|'.join(status_data).encode()).hexdigest()
        except Exception:
            return str(time.time())  # 出错时返回时间戳，强制更新
    
    def _parse_emulator_data(self, emulators) -> Tuple[int, int, int]:
        """高性能批量解析模拟器数据"""
        total_follow_count = 0
        total_dm_count = 0
        processed_count = 0
        
        # 🎯 批量处理，减少函数调用开销
        for row in emulators:
            if len(row) > 8:
                task_status = row[8]
                if task_status and task_status != "-":
                    processed_count += 1
                    
                    # 关注任务解析
                    follow_match = self._follow_pattern.search(task_status)
                    if follow_match:
                        total_follow_count += int(follow_match.group(1))
                    
                    # 私信任务解析
                    dm_match = self._dm_pattern.search(task_status)
                    if dm_match:
                        total_dm_count += int(dm_match.group(1))
        
        return total_follow_count, total_dm_count, processed_count
    
    def _schedule_update_signal(self, follow_count: int, dm_count: int):
        """防抖信号发送 - 避免频繁UI更新"""
        self._pending_update = (follow_count, dm_count)
        self._debounce_timer.start(100)  # 100ms防抖
    
    def _emit_update_signal(self):
        """发送更新信号"""
        if self._pending_update:
            follow_count, dm_count = self._pending_update
            self.summary_updated.emit(follow_count, dm_count)
            self._pending_update = None
    
    def invalidate_cache(self):
        """使缓存失效，强制下次更新"""
        self._cache['last_update_time'] = 0
        self._cache['data_hash'] = None
        self.logger.debug("汇总统计缓存已失效")
    
    def get_cache_info(self) -> Dict:
        """获取缓存信息 - 用于调试"""
        return {
            'cache_age': time.time() - self._cache['last_update_time'],
            'cache_valid': time.time() - self._cache['last_update_time'] < self._cache_ttl,
            'processed_count': self._cache['processed_count'],
            'follow_count': self._cache['total_follow_count'],
            'dm_count': self._cache['total_dm_count']
        }


# 🎯 全局单例实例
_summary_service = None

def get_instagram_summary_service() -> InstagramSummaryService:
    """获取Instagram汇总统计服务单例"""
    global _summary_service
    if _summary_service is None:
        _summary_service = InstagramSummaryService()
    return _summary_service
