"""
Instagram汇总统计服务 - 异步+增量处理版本
功能：高性能统计所有模拟器的关注人数和私信人数
"""

import re
import logging
import time
import hashlib
from typing import Tuple, Dict, List
from PyQt6.QtCore import QObject, pyqtSignal, QThread, QTimer


class AsyncSummaryWorker(QThread):
    """异步汇总统计工作线程"""

    # 结果信号
    result_ready = pyqtSignal(int, int, float)  # follow_count, dm_count, process_time

    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(self.__class__.__name__)

        # 预编译正则表达式
        self._follow_pattern = re.compile(r'关注.*[已完成关注中].*\((\d+)/\d+\)')
        self._dm_pattern = re.compile(r'[（(](?:成功|已发送)(\d+)/(?:目标)?(\d+)[）)]')

        # 工作数据
        self._emulator_data = []
        self._should_process = False

    def process_data(self, emulator_data: List):
        """提交数据处理请求"""
        self._emulator_data = emulator_data.copy()
        self._should_process = True

        if not self.isRunning():
            self.start()

    def run(self):
        """异步处理主循环"""
        try:
            if not self._should_process:
                return

            start_time = time.time()
            follow_count = dm_count = 0

            # CPU密集型处理
            for row in self._emulator_data:
                if len(row) > 8 and row[8] and row[8] != "-":
                    status = row[8]

                    # 使用扩展的解析逻辑
                    single_follow, single_dm = self._parse_single_status(status)
                    follow_count += single_follow
                    dm_count += single_dm

            process_time = (time.time() - start_time) * 1000
            self.result_ready.emit(follow_count, dm_count, process_time)

        except Exception as e:
            self.logger.error(f"异步处理失败: {e}")
            self.result_ready.emit(0, 0, 0)

    def _parse_single_status(self, status: str) -> Tuple[int, int]:
        """解析单个模拟器状态"""
        follow_count = dm_count = 0

        if status and status != "-":
            # 关注解析 - 扩展匹配规则
            follow_patterns = [
                r'关注.*[已完成关注中].*\((\d+)/\d+\)',  # 原有规则
                r'关注.*完成.*?(\d+)',  # 关注完成X个
                r'已关注.*?(\d+)',  # 已关注X个
                r'关注成功.*?(\d+)',  # 关注成功X个
            ]

            for pattern in follow_patterns:
                follow_match = re.search(pattern, status)
                if follow_match:
                    follow_count = int(follow_match.group(1))
                    break

            # 私信解析 - 扩展匹配规则
            dm_patterns = [
                r'[（(](?:成功|已发送)(\d+)/(?:目标)?(\d+)[）)]',  # 原有规则
                r'私信.*?成功.*?(\d+)',  # 私信成功X条
                r'已发送.*?(\d+)',  # 已发送X条
                r'发送完成.*?(\d+)',  # 发送完成X条
            ]

            for pattern in dm_patterns:
                dm_match = re.search(pattern, status)
                if dm_match:
                    dm_count = int(dm_match.group(1))
                    break

        return follow_count, dm_count


class InstagramSummaryService(QObject):
    """Instagram汇总统计服务 - 异步+增量处理版本"""

    # 结果更新信号
    summary_updated = pyqtSignal(int, int)  # follow_count, dm_count

    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(self.__class__.__name__)

        # 增量缓存：记录每个模拟器的统计结果
        self._incremental_cache: Dict[int, Tuple[int, int, str]] = {}  # {emulator_id: (follow, dm, status_hash)}
        self._total_follow = 0
        self._total_dm = 0

        # 异步工作线程
        self._worker = AsyncSummaryWorker()
        self._worker.result_ready.connect(self._on_result_ready)

        # 防抖定时器
        self._debounce_timer = QTimer()
        self._debounce_timer.setSingleShot(True)
        self._debounce_timer.timeout.connect(self._emit_update_signal)
        self._pending_result = None

    def get_summary(self, emulator_data, force_refresh=False) -> Tuple[int, int]:
        """
        获取汇总统计（异步+增量处理）

        Args:
            emulator_data: 模拟器数据列表
            force_refresh: 是否强制刷新

        Returns:
            (关注人数, 私信人数) - 立即返回当前缓存值
        """
        try:
            if not emulator_data:
                return 0, 0

            if force_refresh:
                # 强制刷新：清空增量缓存，重新计算
                self._incremental_cache.clear()
                self._total_follow = 0
                self._total_dm = 0
                self._worker.process_data(emulator_data)
            else:
                # 增量更新：只处理变化的模拟器
                self._process_incremental_update(emulator_data)

            # 立即返回当前缓存值
            return self._total_follow, self._total_dm

        except Exception as e:
            self.logger.error(f"获取汇总统计失败: {e}")
            return 0, 0

    def _process_incremental_update(self, emulator_data: List):
        """增量更新处理"""
        try:
            changed_count = 0

            for i, row in enumerate(emulator_data):
                if len(row) <= 8:
                    continue

                emulator_id = i  # 使用索引作为ID
                current_status = row[8] or "-"
                current_hash = hashlib.md5(current_status.encode()).hexdigest()

                # 检查是否有变化
                if emulator_id in self._incremental_cache:
                    old_follow, old_dm, old_hash = self._incremental_cache[emulator_id]
                    if old_hash == current_hash:
                        continue  # 无变化，跳过

                    # 减去旧值
                    self._total_follow -= old_follow
                    self._total_dm -= old_dm

                # 计算新值
                new_follow, new_dm = self._parse_single_status(current_status)

                # 更新缓存和总计
                self._incremental_cache[emulator_id] = (new_follow, new_dm, current_hash)
                self._total_follow += new_follow
                self._total_dm += new_dm
                changed_count += 1

            # 如果有变化，发送更新信号
            if changed_count > 0:
                self.logger.debug(f"增量更新: {changed_count}个模拟器状态变化")
                self._schedule_update_signal()

        except Exception as e:
            self.logger.error(f"增量更新失败: {e}")

    def _parse_single_status(self, status: str) -> Tuple[int, int]:
        """解析单个模拟器状态"""
        follow_count = dm_count = 0

        if status and status != "-":
            # 调试：记录状态文本
            if "关注" in status or "私信" in status or "成功" in status or "已发送" in status:
                self.logger.debug(f"解析状态文本: {status}")

            # 关注解析 - 扩展匹配规则
            follow_patterns = [
                r'关注.*[已完成关注中].*\((\d+)/\d+\)',  # 原有规则
                r'关注.*完成.*?(\d+)',  # 关注完成X个
                r'已关注.*?(\d+)',  # 已关注X个
                r'关注成功.*?(\d+)',  # 关注成功X个
            ]

            for pattern in follow_patterns:
                follow_match = re.search(pattern, status)
                if follow_match:
                    follow_count = int(follow_match.group(1))
                    self.logger.debug(f"关注匹配成功: {pattern} -> {follow_count}")
                    break

            # 私信解析 - 扩展匹配规则
            dm_patterns = [
                r'[（(](?:成功|已发送)(\d+)/(?:目标)?(\d+)[）)]',  # 原有规则
                r'私信.*?成功.*?(\d+)',  # 私信成功X条
                r'已发送.*?(\d+)',  # 已发送X条
                r'发送完成.*?(\d+)',  # 发送完成X条
            ]

            for pattern in dm_patterns:
                dm_match = re.search(pattern, status)
                if dm_match:
                    dm_count = int(dm_match.group(1))
                    self.logger.debug(f"私信匹配成功: {pattern} -> {dm_count}")
                    break

        return follow_count, dm_count

    def _on_result_ready(self, follow_count: int, dm_count: int, process_time: float):
        """处理异步结果"""
        try:
            self._total_follow = follow_count
            self._total_dm = dm_count

            self.logger.info(f"异步汇总完成: 关注{follow_count}人, 私信{dm_count}条 (耗时{process_time:.1f}ms)")

            # 发送更新信号
            self._schedule_update_signal()

        except Exception as e:
            self.logger.error(f"处理异步结果失败: {e}")

    def _schedule_update_signal(self):
        """调度更新信号（防抖）"""
        self._pending_result = (self._total_follow, self._total_dm)
        self._debounce_timer.start(50)  # 50ms防抖

    def _emit_update_signal(self):
        """发送更新信号"""
        if self._pending_result:
            follow_count, dm_count = self._pending_result
            self.summary_updated.emit(follow_count, dm_count)
            self._pending_result = None

    def get_current_summary(self) -> Tuple[int, int]:
        """获取当前缓存的汇总结果"""
        return self._total_follow, self._total_dm

    def clear_cache(self):
        """清空缓存"""
        self._incremental_cache.clear()
        self._total_follow = 0
        self._total_dm = 0


def create_summary_service():
    """创建汇总统计服务实例"""
    return InstagramSummaryService()
