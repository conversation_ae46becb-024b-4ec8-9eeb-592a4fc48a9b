"""
Instagram汇总统计服务 - 纯异步高性能版本
"""

import time
import hashlib
import re
import logging
from typing import Dict, <PERSON><PERSON>, List
from PyQt6.QtCore import QObject, pyqtSignal, QThread, QMutex, QMutexLocker


class AsyncSummaryWorker(QThread):
    """异步汇总统计工作线程"""

    # 结果信号
    result_ready = pyqtSignal(int, int, str)  # follow_count, dm_count, data_hash

    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(self.__class__.__name__)

        # 线程安全
        self._mutex = QMutex()
        self._emulator_data = []
        self._should_process = False

        # 预编译正则表达式
        self._follow_pattern = re.compile(r'关注.*[已完成关注中].*\((\d+)/\d+\)')
        self._dm_pattern = re.compile(r'[（(](?:成功|已发送)(\d+)/(?:目标)?(\d+)[）)]')

    def process_data(self, emulator_data: List):
        """提交数据处理请求"""
        with QMutexLocker(self._mutex):
            self._emulator_data = emulator_data.copy()
            self._should_process = True

        if not self.isRunning():
            self.start()

    def run(self):
        """异步处理主循环"""
        try:
            with QMutexLocker(self._mutex):
                if not self._should_process:
                    return

                data_to_process = self._emulator_data.copy()
                self._should_process = False

            # CPU密集型处理
            follow_count, dm_count = self._parse_data(data_to_process)
            data_hash = self._calculate_hash(data_to_process)

            # 发送结果
            self.result_ready.emit(follow_count, dm_count, data_hash)

        except Exception as e:
            self.logger.error(f"异步处理失败: {e}")

    def _parse_data(self, emulator_data: List) -> Tuple[int, int]:
        """解析模拟器数据"""
        follow_count = dm_count = 0

        for row in emulator_data:
            if len(row) > 8 and row[8] and row[8] != "-":
                status = row[8]

                # 关注解析
                follow_match = self._follow_pattern.search(status)
                if follow_match:
                    follow_count += int(follow_match.group(1))

                # 私信解析
                dm_match = self._dm_pattern.search(status)
                if dm_match:
                    dm_count += int(dm_match.group(1))

        return follow_count, dm_count

    def _calculate_hash(self, emulator_data: List) -> str:
        """计算数据哈希"""
        try:
            status_list = [row[8] or "-" for row in emulator_data if len(row) > 8]
            return hashlib.md5('|'.join(status_list).encode()).hexdigest()
        except Exception:
            return str(time.time())


class InstagramSummaryService(QObject):
    """Instagram汇总统计服务 - 纯异步单例"""

    # 统计更新信号
    summary_updated = pyqtSignal(int, int)  # follow_count, dm_count

    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if self._initialized:
            return

        super().__init__()
        self.logger = logging.getLogger(self.__class__.__name__)

        # 缓存系统
        self._cache = {
            'follow_count': 0,
            'dm_count': 0,
            'data_hash': None,
            'last_update': 0
        }
        self._cache_ttl = 2.0  # 2秒缓存

        # 异步工作线程
        self._worker = AsyncSummaryWorker()
        self._worker.result_ready.connect(self._on_result_ready)

        self._initialized = True
        self.logger.info("Instagram汇总统计服务初始化完成（纯异步）")
    
    def get_summary(self, table_model=None, force_refresh=False) -> Tuple[int, int]:
        """获取汇总统计 - 纯异步处理"""
        try:
            if not table_model or not hasattr(table_model, 'emulators'):
                return 0, 0

            current_time = time.time()
            emulator_data = table_model.emulators

            # 计算数据哈希
            status_list = [row[8] or "-" for row in emulator_data if len(row) > 8]
            data_hash = hashlib.md5('|'.join(status_list).encode()).hexdigest()

            # 检查缓存
            if (not force_refresh and
                current_time - self._cache['last_update'] < self._cache_ttl and
                data_hash == self._cache['data_hash']):
                return self._cache['follow_count'], self._cache['dm_count']

            # 提交异步处理
            self._worker.process_data(emulator_data)

            # 立即返回缓存值
            return self._cache['follow_count'], self._cache['dm_count']

        except Exception as e:
            self.logger.error(f"获取汇总统计失败: {e}")
            return 0, 0

    def _on_result_ready(self, follow_count: int, dm_count: int, data_hash: str):
        """处理异步结果"""
        try:
            # 更新缓存
            self._cache.update({
                'follow_count': follow_count,
                'dm_count': dm_count,
                'data_hash': data_hash,
                'last_update': time.time()
            })

            # 发送更新信号
            self.summary_updated.emit(follow_count, dm_count)

            self.logger.info(f"异步汇总完成: 关注{follow_count}人, 私信{dm_count}条")

        except Exception as e:
            self.logger.error(f"处理异步结果失败: {e}")

    def invalidate_cache(self):
        """使缓存失效"""
        self._cache['last_update'] = 0
        self._cache['data_hash'] = None

    def cleanup(self):
        """清理资源"""
        try:
            if self._worker.isRunning():
                self._worker.quit()
                self._worker.wait(2000)
            self.logger.info("服务已清理")
        except Exception as e:
            self.logger.error(f"清理失败: {e}")
    



# 全局单例获取函数
def get_instagram_summary_service() -> InstagramSummaryService:
    """获取Instagram汇总统计服务单例"""
    return InstagramSummaryService()
