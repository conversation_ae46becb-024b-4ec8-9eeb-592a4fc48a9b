"""
Instagram汇总统计服务 - 极简版本（带缓存优化）
功能：统计所有模拟器的关注人数和私信人数
"""

import re
import logging
import time
import hashlib
from typing import Tuple


class InstagramSummaryService:
    """Instagram汇总统计服务 - 极简版本（带缓存优化）"""

    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)

        # 预编译正则表达式
        self._follow_pattern = re.compile(r'关注.*[已完成关注中].*\((\d+)/\d+\)')
        self._dm_pattern = re.compile(r'[（(](?:成功|已发送)(\d+)/(?:目标)?(\d+)[）)]')

        # 简单缓存机制
        self._cache = {
            'data_hash': None,
            'follow_count': 0,
            'dm_count': 0,
            'last_update': 0
        }
        self._cache_ttl = 3.0  # 3秒缓存
    
    def get_summary(self, emulator_data, force_refresh=False) -> Tuple[int, int]:
        """
        获取汇总统计（带缓存优化）

        Args:
            emulator_data: 模拟器数据列表
            force_refresh: 是否强制刷新缓存

        Returns:
            (关注人数, 私信人数)
        """
        try:
            if not emulator_data:
                return 0, 0

            current_time = time.time()

            # 计算数据哈希（只基于任务状态列）
            status_data = []
            for row in emulator_data:
                if len(row) > 8:
                    status_data.append(row[8] or "-")

            data_hash = hashlib.md5('|'.join(status_data).encode()).hexdigest()

            # 检查缓存
            if (not force_refresh and
                current_time - self._cache['last_update'] < self._cache_ttl and
                data_hash == self._cache['data_hash']):
                # 使用缓存
                return self._cache['follow_count'], self._cache['dm_count']

            # 重新计算
            start_time = time.time()
            total_follow_count = 0
            total_dm_count = 0

            # 遍历所有模拟器数据
            for row in emulator_data:
                if len(row) > 8:  # 确保有任务状态列
                    task_status = row[8]
                    if task_status and task_status != "-":
                        # 解析关注数
                        follow_match = self._follow_pattern.search(task_status)
                        if follow_match:
                            total_follow_count += int(follow_match.group(1))

                        # 解析私信数
                        dm_match = self._dm_pattern.search(task_status)
                        if dm_match:
                            total_dm_count += int(dm_match.group(1))

            # 更新缓存
            self._cache.update({
                'data_hash': data_hash,
                'follow_count': total_follow_count,
                'dm_count': total_dm_count,
                'last_update': current_time
            })

            # 记录性能
            process_time = (time.time() - start_time) * 1000
            self.logger.debug(f"汇总统计计算完成: {len(emulator_data)}个模拟器, 耗时{process_time:.1f}ms")

            return total_follow_count, total_dm_count

        except Exception as e:
            self.logger.error(f"汇总统计计算失败: {e}")
            return 0, 0


def create_summary_service():
    """创建汇总统计服务实例"""
    return InstagramSummaryService()
