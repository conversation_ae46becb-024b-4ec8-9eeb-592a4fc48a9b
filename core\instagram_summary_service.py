"""
Instagram汇总统计服务 - 极简版本
功能：统计所有模拟器的关注人数和私信人数
"""

import re
import logging
from typing import Tuple


class InstagramSummaryService:
    """Instagram汇总统计服务 - 极简版本"""

    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)

        # 预编译正则表达式
        self._follow_pattern = re.compile(r'关注.*[已完成关注中].*\((\d+)/\d+\)')
        self._dm_pattern = re.compile(r'[（(](?:成功|已发送)(\d+)/(?:目标)?(\d+)[）)]')

    def get_summary(self, emulator_data, force_refresh=True) -> Tuple[int, int]:
        """获取汇总统计"""
        try:
            follow_count = dm_count = 0

            # 遍历所有模拟器数据
            for row in emulator_data:
                if len(row) > 8 and row[8] and row[8] != "-":
                    status = row[8]

                    # 关注解析
                    follow_match = self._follow_pattern.search(status)
                    if follow_match:
                        follow_count += int(follow_match.group(1))

                    # 私信解析
                    dm_match = self._dm_pattern.search(status)
                    if dm_match:
                        dm_count += int(dm_match.group(1))

            self.logger.info(f"汇总统计完成: 关注{follow_count}人, 私信{dm_count}条")
            return follow_count, dm_count

        except Exception as e:
            self.logger.error(f"汇总统计失败: {e}")
            return 0, 0