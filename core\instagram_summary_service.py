"""
Instagram汇总统计服务 - 高性能版本
提供缓存、增量更新、异步处理等优化功能
"""

import time
import hashlib
import re
import logging
from typing import Dict, Optional, Tuple, List
from PyQt6.QtCore import QObject, pyqtSignal, QTimer, QThread, QMutex, QMutexLocker


class SummaryWorkerThread(QThread):
    """异步汇总统计工作线程 - 避免UI线程阻塞"""

    # 工作完成信号
    work_finished = pyqtSignal(int, int, int, str)  # follow_count, dm_count, processed_count, data_hash

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger(self.__class__.__name__)

        # 🎯 线程安全的工作队列
        self._mutex = QMutex()
        self._work_queue = []
        self._is_working = False

        # 🎯 预编译正则表达式
        self._follow_pattern = re.compile(r'关注.*[已完成关注中].*\((\d+)/\d+\)')
        self._dm_pattern = re.compile(r'[（(](?:成功|已发送)(\d+)/(?:目标)?(\d+)[）)]')

    def add_work(self, emulator_data: List, request_id: str):
        """添加工作任务到队列"""
        with QMutexLocker(self._mutex):
            # 🎯 去重：如果队列中已有相同数据，替换而不是追加
            self._work_queue = [(data, rid) for data, rid in self._work_queue if rid != request_id]
            self._work_queue.append((emulator_data.copy(), request_id))

            if not self._is_working and not self.isRunning():
                self.start()

    def run(self):
        """异步工作线程主循环"""
        try:
            while True:
                # 🎯 获取工作任务
                work_item = None
                with QMutexLocker(self._mutex):
                    if self._work_queue:
                        work_item = self._work_queue.pop(0)
                        self._is_working = True
                    else:
                        self._is_working = False
                        break

                if work_item:
                    emulator_data, request_id = work_item

                    # 🎯 执行CPU密集型计算
                    follow_count, dm_count, processed_count = self._process_emulator_data(emulator_data)

                    # 🎯 计算数据哈希
                    data_hash = self._calculate_data_hash(emulator_data)

                    # 🎯 发送结果信号
                    self.work_finished.emit(follow_count, dm_count, processed_count, data_hash)

                    self.logger.debug(f"异步处理完成: 关注{follow_count}, 私信{dm_count}, 处理{processed_count}行")

        except Exception as e:
            self.logger.error(f"异步工作线程异常: {e}")
        finally:
            with QMutexLocker(self._mutex):
                self._is_working = False

    def _process_emulator_data(self, emulator_data: List) -> Tuple[int, int, int]:
        """处理模拟器数据 - CPU密集型操作"""
        total_follow_count = 0
        total_dm_count = 0
        processed_count = 0

        for row in emulator_data:
            if len(row) > 8:
                task_status = row[8]
                if task_status and task_status != "-":
                    processed_count += 1

                    # 关注任务解析
                    follow_match = self._follow_pattern.search(task_status)
                    if follow_match:
                        total_follow_count += int(follow_match.group(1))

                    # 私信任务解析
                    dm_match = self._dm_pattern.search(task_status)
                    if dm_match:
                        total_dm_count += int(dm_match.group(1))

        return total_follow_count, total_dm_count, processed_count

    def _calculate_data_hash(self, emulator_data: List) -> str:
        """计算数据哈希值"""
        try:
            status_data = []
            for row in emulator_data:
                if len(row) > 8:
                    status_data.append(row[8] or "-")

            return hashlib.md5('|'.join(status_data).encode()).hexdigest()
        except Exception:
            return str(time.time())


class InstagramSummaryService(QObject):
    """Instagram汇总统计服务 - 单例模式，避免重复计算"""
    
    # 信号：统计数据更新
    summary_updated = pyqtSignal(int, int)  # follow_count, dm_count
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return

        super().__init__()
        self.logger = logging.getLogger(self.__class__.__name__)

        # 🎯 高性能缓存系统
        self._cache = {
            'total_follow_count': 0,
            'total_dm_count': 0,
            'last_update_time': 0,
            'data_hash': None,
            'processed_count': 0
        }

        # 🎯 性能配置
        self._cache_ttl = 3.0  # 缓存有效期3秒
        self._async_threshold = 50  # 超过50行数据时使用异步处理

        # 🎯 异步工作线程
        self._worker_thread = SummaryWorkerThread(self)
        self._worker_thread.work_finished.connect(self._on_async_work_finished)
        self._request_counter = 0

        # 🎯 预编译正则表达式（用于小数据量同步处理）
        self._follow_pattern = re.compile(r'关注.*[已完成关注中].*\((\d+)/\d+\)')
        self._dm_pattern = re.compile(r'[（(](?:成功|已发送)(\d+)/(?:目标)?(\d+)[）)]')

        # 🎯 防抖定时器，避免频繁更新
        self._debounce_timer = QTimer()
        self._debounce_timer.setSingleShot(True)
        self._debounce_timer.timeout.connect(self._emit_update_signal)
        self._pending_update = None

        self._initialized = True
        self.logger.info("Instagram汇总统计服务初始化完成（支持异步处理）")
    
    def get_summary(self, table_model=None, force_refresh=False) -> Tuple[int, int]:
        """获取汇总统计 - 智能异步/同步处理"""
        try:
            current_time = time.time()

            # 🎯 缓存检查
            if not force_refresh and current_time - self._cache['last_update_time'] < self._cache_ttl:
                return self._cache['total_follow_count'], self._cache['total_dm_count']

            if not table_model or not hasattr(table_model, 'emulators'):
                return 0, 0

            emulator_data = table_model.emulators
            data_count = len(emulator_data)

            # 🎯 数据变化检测
            data_hash = self._calculate_data_hash_sync(emulator_data)
            if not force_refresh and data_hash == self._cache['data_hash']:
                self._cache['last_update_time'] = current_time
                return self._cache['total_follow_count'], self._cache['total_dm_count']

            # 🎯 智能选择处理方式
            if data_count > self._async_threshold:
                # 大数据量：异步处理，立即返回缓存值
                self.logger.info(f"数据量大({data_count}行)，启用异步处理")
                self._request_counter += 1
                request_id = f"async_{self._request_counter}_{current_time}"
                self._worker_thread.add_work(emulator_data, request_id)

                # 返回当前缓存值，异步结果会通过信号更新
                return self._cache['total_follow_count'], self._cache['total_dm_count']
            else:
                # 小数据量：同步处理
                self.logger.debug(f"数据量小({data_count}行)，使用同步处理")
                follow_count, dm_count, processed = self._parse_emulator_data_sync(emulator_data)

                # 🎯 更新缓存
                self._cache.update({
                    'total_follow_count': follow_count,
                    'total_dm_count': dm_count,
                    'last_update_time': current_time,
                    'data_hash': data_hash,
                    'processed_count': processed
                })

                # 🎯 防抖信号发送
                self._schedule_update_signal(follow_count, dm_count)

                return follow_count, dm_count

        except Exception as e:
            self.logger.error(f"获取汇总统计失败: {e}")
            return 0, 0
    
    def _calculate_data_hash_sync(self, emulators) -> str:
        """计算数据哈希值 - 同步版本"""
        try:
            status_data = []
            for row in emulators:
                if len(row) > 8:
                    status_data.append(row[8] or "-")

            return hashlib.md5('|'.join(status_data).encode()).hexdigest()
        except Exception:
            return str(time.time())  # 出错时返回时间戳，强制更新

    def _parse_emulator_data_sync(self, emulators) -> Tuple[int, int, int]:
        """同步解析模拟器数据 - 用于小数据量"""
        total_follow_count = 0
        total_dm_count = 0
        processed_count = 0

        for row in emulators:
            if len(row) > 8:
                task_status = row[8]
                if task_status and task_status != "-":
                    processed_count += 1

                    # 关注任务解析
                    follow_match = self._follow_pattern.search(task_status)
                    if follow_match:
                        total_follow_count += int(follow_match.group(1))

                    # 私信任务解析
                    dm_match = self._dm_pattern.search(task_status)
                    if dm_match:
                        total_dm_count += int(dm_match.group(1))

        return total_follow_count, total_dm_count, processed_count

    def _on_async_work_finished(self, follow_count: int, dm_count: int, processed_count: int, data_hash: str):
        """处理异步工作完成信号"""
        try:
            current_time = time.time()

            # 🎯 更新缓存
            self._cache.update({
                'total_follow_count': follow_count,
                'total_dm_count': dm_count,
                'last_update_time': current_time,
                'data_hash': data_hash,
                'processed_count': processed_count
            })

            # 🎯 立即发送更新信号（异步结果不需要防抖）
            self.summary_updated.emit(follow_count, dm_count)

            self.logger.info(f"异步汇总完成: 关注{follow_count}人, 私信{dm_count}条, 处理{processed_count}行")

        except Exception as e:
            self.logger.error(f"处理异步工作结果失败: {e}")
    
    def _schedule_update_signal(self, follow_count: int, dm_count: int):
        """防抖信号发送 - 避免频繁UI更新"""
        self._pending_update = (follow_count, dm_count)
        self._debounce_timer.start(100)  # 100ms防抖
    
    def _emit_update_signal(self):
        """发送更新信号"""
        if self._pending_update:
            follow_count, dm_count = self._pending_update
            self.summary_updated.emit(follow_count, dm_count)
            self._pending_update = None
    
    def invalidate_cache(self):
        """使缓存失效，强制下次更新"""
        self._cache['last_update_time'] = 0
        self._cache['data_hash'] = None
        self.logger.debug("汇总统计缓存已失效")
    
    def get_cache_info(self) -> Dict:
        """获取缓存信息 - 用于调试"""
        return {
            'cache_age': time.time() - self._cache['last_update_time'],
            'cache_valid': time.time() - self._cache['last_update_time'] < self._cache_ttl,
            'processed_count': self._cache['processed_count'],
            'follow_count': self._cache['total_follow_count'],
            'dm_count': self._cache['total_dm_count'],
            'async_threshold': self._async_threshold,
            'worker_running': self._worker_thread.isRunning()
        }

    def set_async_threshold(self, threshold: int):
        """设置异步处理阈值"""
        self._async_threshold = max(10, threshold)  # 最小10行
        self.logger.info(f"异步处理阈值设置为: {self._async_threshold}行")

    def cleanup(self):
        """清理资源"""
        try:
            if self._worker_thread.isRunning():
                self._worker_thread.quit()
                self._worker_thread.wait(3000)  # 等待3秒
            self.logger.info("Instagram汇总统计服务已清理")
        except Exception as e:
            self.logger.error(f"清理服务失败: {e}")


# 🎯 全局单例实例
_summary_service = None

def get_instagram_summary_service() -> InstagramSummaryService:
    """获取Instagram汇总统计服务单例"""
    global _summary_service
    if _summary_service is None:
        _summary_service = InstagramSummaryService()
    return _summary_service
