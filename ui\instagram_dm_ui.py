#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 Instagram粉丝私信配置界面模块
========================================
功能描述: Instagram粉丝私信任务的配置界面，复用监控设置页面的结构和样式
主要方法: __init__(), init_ui(), setup_connections(), load_settings()
调用关系: 被任务管理页面调用，通过事件总线与业务层通信
注意事项:
- UI层只负责界面展示，业务逻辑通过事件总线处理
- 完全复用settings_ui.py的布局结构和组件样式
- 使用统一配置管理器进行设置保存和加载
- 所有配置变更通过信号通知其他组件
========================================
"""

# ============================================================================
# 🎯 Instagram私信配置界面主类功能组
# ============================================================================
# 功能描述: Instagram私信配置界面主类，负责界面创建和事件处理
# 调用关系: 被任务管理页面调用，通过事件总线与业务层通信
# 注意事项: UI层只负责界面展示，业务逻辑通过事件总线处理
# ============================================================================

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
                           QLabel, QScrollArea, QGridLayout, QFileDialog, QTextEdit,
                           QRadioButton, QButtonGroup)
from PyQt6.QtCore import pyqtSignal, QTimer
from PyQt6.QtGui import QColor
import logging

# 导入自定义UI组件
from .styled_widgets import (StyledButton, ModernSpinBox, ModernToggleSwitch,
                           StatusCard, HeartbeatIndicator, StyledLineEdit)
from .style_manager import (StyleManager, create_secondary_button, create_warning_button,
                          create_danger_button)


class InstagramDMUI(QWidget):
    """
    Instagram粉丝私信配置界面
    ========================================
    功能描述: Instagram私信任务的参数配置和状态显示界面
    主要功能:
    - 基础配置：私信数量、延迟设置、消息间隔
    - 高级配置：记录文件、私信内容、发送模式
    - 任务状态：实时显示任务执行状态和统计信息
    - 配置管理：与simple_config.py统一配置系统集成
    ========================================
    """

    # 🎯 信号定义 - UI与业务层通信
    setting_changed = pyqtSignal(str, object)      # 配置变更信号：参数名, 新值
    task_start_requested = pyqtSignal()            # 任务启动请求信号
    task_stop_requested = pyqtSignal()             # 任务停止请求信号

    def __init__(self, parent=None, config_manager=None):
        super().__init__(parent)

        # 🎯 配置管理器初始化 - 与统一配置系统集成
        if config_manager is not None:
            self.config_manager = config_manager
        else:
            # 兼容性：如果没有传入配置管理器，创建新实例
            from core.simple_config import get_config_manager
            self.config_manager = get_config_manager()

        # 🎯 日志管理器初始化
        self.logger = logging.getLogger(self.__class__.__name__)

        # 🎯 性能优化：使用共享汇总统计服务
        from core.instagram_summary_service import get_instagram_summary_service
        self._summary_service = get_instagram_summary_service()
        self._summary_service.summary_updated.connect(self._on_summary_updated)

        # 🎯 UI初始化顺序 - 确保正确的依赖关系
        self.init_ui()           # 1. 创建UI控件和布局
        self.load_settings()     # 2. 从配置文件加载设置值到UI
        self.setup_connections() # 3. 连接UI控件信号到配置保存

    def init_ui(self):
        """创建界面布局"""
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("QScrollArea { border: none; }")

        # 创建内容容器
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(30)

        # 标题
        title_label = QLabel("Instagram粉丝私信配置")
        title_label.setStyleSheet("font-size: 22px; font-weight: bold; color: #333; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # 实时任务状态区域
        status_section = self.create_task_status_section()
        layout.addWidget(status_section)

        # 基础配置参数区域
        basic_config_section = self.create_basic_config_section()
        layout.addWidget(basic_config_section)

        # 高级配置区域
        advanced_config_section = self.create_advanced_config_section()
        layout.addWidget(advanced_config_section)

        layout.addStretch()

        scroll_area.setWidget(content_widget)

        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(scroll_area)

    def create_task_status_section(self):
        """创建实时任务状态区域"""
        status_group = QGroupBox("🎯 实时任务状态")
        status_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                border: 2px solid #e3f2fd;
                border-radius: 12px;
                margin-top: 12px;
                padding-top: 15px;
                background-color: #f3f9ff;
                min-height: 200px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 12px 0 12px;
                background-color: #f3f9ff;
                color: #1976D2;
            }
        """)

        layout = QVBoxLayout(status_group)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 🎯 优化：全局汇总统计区域 - 只保留总关注数和总私信数
        summary_layout = QHBoxLayout()
        summary_layout.setSpacing(40)  # 增加间距
        summary_layout.setContentsMargins(20, 10, 20, 10)  # 增加边距

        self.total_follow_card = StatusCard("总关注数", "0", "👥")
        self.total_dm_card = StatusCard("总私信数", "0", "💬")

        summary_layout.addWidget(self.total_follow_card)
        summary_layout.addWidget(self.total_dm_card)

        # 添加弹性空间，让卡片居中显示
        summary_layout.addStretch()

        layout.addLayout(summary_layout)

        # 系统状态和控制按钮区域
        control_layout = QHBoxLayout()
        control_layout.setSpacing(15)

        status_label = QLabel("系统状态:")
        status_label.setStyleSheet("font-weight: bold; color: #333; font-size: 14px;")

        self.system_status_indicator = HeartbeatIndicator()
        self.system_status_indicator.set_active(True)
        self.system_status_indicator.set_color(QColor("#4CAF50"))

        # 控制按钮
        self.refresh_btn = create_secondary_button("🔄 刷新", (80, 30))
        self.stop_task_btn = create_danger_button("⏹ 停止任务", (100, 30))

        control_layout.addWidget(status_label)
        control_layout.addWidget(self.system_status_indicator)
        control_layout.addWidget(self.refresh_btn)
        control_layout.addWidget(self.stop_task_btn)
        control_layout.addStretch()

        layout.addLayout(control_layout)

        return status_group

    def create_basic_config_section(self):
        """
        创建基础配置参数区域
        ========================================
        功能描述: 创建Instagram私信任务的基础参数配置界面
        包含控件:
        - 私信数量：控制每次任务发送的私信条数(1-1000)
        - 用户间延迟：控制发送给不同用户的时间间隔(100-60000毫秒)
        - 消息间延迟：控制发送单条消息后的等待时间(100-10000毫秒)
        - 私信前撤回：控制是否在发送前撤回之前的消息
        ========================================
        """
        basic_group = QGroupBox("🎯 基础配置参数")
        basic_group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #333;
                border: 2px solid #e8f5e8;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: #f8fff8;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background-color: #f8fff8;
                color: #4CAF50;
            }
        """)

        # 使用QGridLayout，确保控件显示一致性
        layout = QGridLayout(basic_group)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setVerticalSpacing(15)
        layout.setHorizontalSpacing(5)
        layout.setColumnMinimumWidth(0, 120)
        layout.setColumnStretch(1, 0)
        layout.setColumnStretch(2, 1)

        row = 0

        # 私信数量
        label = QLabel("私信数量:")
        label.setStyleSheet("font-size: 13px; color: #333; font-weight: 500;")
        self.message_count_spinbox = ModernSpinBox(suffix=" 条")
        self.message_count_spinbox.setRange(1, 1000)
        self.message_count_spinbox.setFixedWidth(200)
        layout.addWidget(label, row, 0)
        layout.addWidget(self.message_count_spinbox, row, 1)
        row += 1



        # 用户间延迟
        label = QLabel("用户间延迟:")
        label.setStyleSheet("font-size: 13px; color: #333; font-weight: 500;")
        delay_widget = QWidget()
        delay_layout = QHBoxLayout(delay_widget)
        delay_layout.setContentsMargins(0, 0, 0, 0)
        delay_layout.setSpacing(10)

        self.delay_min_spinbox = ModernSpinBox()
        self.delay_min_spinbox.setRange(100, 60000)  # 100毫秒到60秒
        self.delay_min_spinbox.setFixedWidth(100)
        self.delay_min_spinbox.spinbox.setSuffix("  毫秒")

        delay_to_label = QLabel("到")
        delay_to_label.setStyleSheet("font-size: 13px; color: #666;")

        self.delay_max_spinbox = ModernSpinBox()
        self.delay_max_spinbox.setRange(100, 60000)  # 100毫秒到60秒
        self.delay_max_spinbox.setFixedWidth(100)
        self.delay_max_spinbox.spinbox.setSuffix("  毫秒")

        delay_layout.addWidget(self.delay_min_spinbox)
        delay_layout.addWidget(delay_to_label)
        delay_layout.addWidget(self.delay_max_spinbox)
        delay_layout.addStretch()

        layout.addWidget(label, row, 0)
        layout.addWidget(delay_widget, row, 1, 1, 2)
        row += 1

        # 消息间延迟
        label = QLabel("消息间延迟:")
        label.setStyleSheet("font-size: 13px; color: #333; font-weight: 500;")
        self.message_delay_spinbox = ModernSpinBox(suffix=" 毫秒")
        self.message_delay_spinbox.setRange(100, 10000)  # 100毫秒到10秒
        self.message_delay_spinbox.setFixedWidth(200)
        layout.addWidget(label, row, 0)
        layout.addWidget(self.message_delay_spinbox, row, 1)
        row += 1

        # 私信前撤回开关
        label = QLabel("私信前撤回:")
        label.setStyleSheet("font-size: 13px; color: #333; font-weight: 500;")
        self.recall_before_dm_switch = ModernToggleSwitch()
        layout.addWidget(label, row, 0)
        layout.addWidget(self.recall_before_dm_switch, row, 1)

        return basic_group

    def create_advanced_config_section(self):
        """
        创建高级配置区域
        ========================================
        功能描述: 创建Instagram私信任务的高级参数配置界面
        包含控件:
        - 记录文件路径：保存已发送用户列表的文件路径
        - 发送模式选择：仅内容1/仅内容2/随机选择
        - 私信内容1：第一组消息模板，支持|分隔多个选项
        - 私信内容2：第二组消息模板，支持|分隔多个选项
        ========================================
        """
        advanced_group = QGroupBox("🎯 高级配置")
        advanced_group.setStyleSheet(StyleManager.get_groupbox_style('warning'))

        # 使用QGridLayout，确保控件显示一致性
        layout = QGridLayout(advanced_group)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setVerticalSpacing(15)
        layout.setHorizontalSpacing(5)
        layout.setColumnMinimumWidth(0, 120)
        layout.setColumnStretch(1, 0)
        layout.setColumnStretch(2, 1)

        row = 0

        # 记录文件路径
        label = QLabel("记录文件路径:")
        label.setStyleSheet(StyleManager.get_label_style('form_label'))

        path_widget = QWidget()
        path_layout = QHBoxLayout(path_widget)
        path_layout.setContentsMargins(0, 0, 0, 0)
        path_layout.setSpacing(10)

        self.record_file_path_input = StyledLineEdit()
        self.record_file_path_input.setMinimumWidth(300)

        self.browse_file_btn = create_secondary_button("浏览", (60, 30))

        path_layout.addWidget(self.record_file_path_input)
        path_layout.addWidget(self.browse_file_btn)
        path_layout.addStretch()

        layout.addWidget(label, row, 0)
        layout.addWidget(path_widget, row, 1, 1, 2)
        row += 1

        # 私信内容 - 左右排列
        content_label = QLabel("私信内容:")
        content_label.setStyleSheet("font-size: 13px; color: #333; font-weight: 500;")

        # 发送模式选择
        send_mode_widget = QWidget()
        send_mode_layout = QHBoxLayout(send_mode_widget)
        send_mode_layout.setContentsMargins(0, 0, 0, 0)
        send_mode_layout.setSpacing(20)

        mode_label = QLabel("发送模式:")
        mode_label.setStyleSheet("font-size: 13px; color: #666; font-weight: 500;")

        # 创建单选按钮组
        self.send_mode_group = QButtonGroup()

        self.mode_content1_radio = QRadioButton("仅发送私信内容1")
        self.mode_content1_radio.setStyleSheet("""
            QRadioButton {
                font-size: 13px;
                color: #333;
                font-weight: 500;
                spacing: 8px;
            }
            QRadioButton::indicator {
                width: 16px;
                height: 16px;
                border-radius: 8px;
                border: 2px solid #e0e0e0;
                background-color: white;
            }
            QRadioButton::indicator:checked {
                border-color: #2196F3;
                background-color: #2196F3;
            }
            QRadioButton::indicator:hover {
                border-color: #90CAF9;
            }
        """)

        self.mode_content2_radio = QRadioButton("仅发送私信内容2")
        self.mode_content2_radio.setStyleSheet("""
            QRadioButton {
                font-size: 13px;
                color: #333;
                font-weight: 500;
                spacing: 8px;
            }
            QRadioButton::indicator {
                width: 16px;
                height: 16px;
                border-radius: 8px;
                border: 2px solid #e0e0e0;
                background-color: white;
            }
            QRadioButton::indicator:checked {
                border-color: #2196F3;
                background-color: #2196F3;
            }
            QRadioButton::indicator:hover {
                border-color: #90CAF9;
            }
        """)

        self.mode_both_radio = QRadioButton("分段发送私信内容1，私信内容2")
        self.mode_both_radio.setStyleSheet("""
            QRadioButton {
                font-size: 13px;
                color: #333;
                font-weight: 500;
                spacing: 8px;
            }
            QRadioButton::indicator {
                width: 16px;
                height: 16px;
                border-radius: 8px;
                border: 2px solid #e0e0e0;
                background-color: white;
            }
            QRadioButton::indicator:checked {
                border-color: #2196F3;
                background-color: #2196F3;
            }
            QRadioButton::indicator:hover {
                border-color: #90CAF9;
            }
        """)

        # 默认选择第一个选项
        self.mode_content1_radio.setChecked(True)

        # 添加到按钮组
        self.send_mode_group.addButton(self.mode_content1_radio, 1)
        self.send_mode_group.addButton(self.mode_content2_radio, 2)
        self.send_mode_group.addButton(self.mode_both_radio, 3)

        send_mode_layout.addWidget(mode_label)
        send_mode_layout.addWidget(self.mode_content1_radio)
        send_mode_layout.addWidget(self.mode_content2_radio)
        send_mode_layout.addWidget(self.mode_both_radio)
        send_mode_layout.addStretch()

        # 创建左右排列的容器
        content_widget = QWidget()
        content_layout = QHBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(15)

        # 左侧私信内容1
        content1_container = QWidget()
        content1_layout = QVBoxLayout(content1_container)
        content1_layout.setContentsMargins(0, 0, 0, 0)
        content1_layout.setSpacing(5)

        content1_label = QLabel("私信内容1:")
        content1_label.setStyleSheet("font-size: 13px; color: #666; font-weight: 500;")

        self.message_content_1_input = QTextEdit()
        self.message_content_1_input.setPlaceholderText("支持换行、空格等格式\n多个内容用|分隔")
        self.message_content_1_input.setMinimumWidth(300)
        self.message_content_1_input.setMinimumHeight(160)
        self.message_content_1_input.setStyleSheet("""
            QTextEdit {
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                padding: 8px;
                font-size: 13px;
                background-color: white;
                color: #333;
                font-family: 'Microsoft YaHei', sans-serif;
            }
            QTextEdit:focus {
                border-color: #2196F3;
                background-color: #f3f9ff;
                outline: none;
            }
            QTextEdit:hover {
                border-color: #90CAF9;
            }
        """)

        content1_layout.addWidget(content1_label)
        content1_layout.addWidget(self.message_content_1_input)

        # 右侧私信内容2
        content2_container = QWidget()
        content2_layout = QVBoxLayout(content2_container)
        content2_layout.setContentsMargins(0, 0, 0, 0)
        content2_layout.setSpacing(5)

        content2_label = QLabel("私信内容2:")
        content2_label.setStyleSheet("font-size: 13px; color: #666; font-weight: 500;")

        self.message_content_2_input = QTextEdit()
        self.message_content_2_input.setPlaceholderText("支持换行、空格等格式\n多个内容用|分隔")
        self.message_content_2_input.setMinimumWidth(300)
        self.message_content_2_input.setMinimumHeight(160)
        self.message_content_2_input.setStyleSheet("""
            QTextEdit {
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                padding: 8px;
                font-size: 13px;
                background-color: white;
                color: #333;
                font-family: 'Microsoft YaHei', sans-serif;
            }
            QTextEdit:focus {
                border-color: #2196F3;
                background-color: #f3f9ff;
                outline: none;
            }
            QTextEdit:hover {
                border-color: #90CAF9;
            }
        """)

        content2_layout.addWidget(content2_label)
        content2_layout.addWidget(self.message_content_2_input)

        # 添加到水平布局
        content_layout.addWidget(content1_container)
        content_layout.addWidget(content2_container)

        layout.addWidget(content_label, row, 0)
        layout.addWidget(send_mode_widget, row, 1, 1, 2)
        row += 1

        # 私信内容输入框
        layout.addWidget(QLabel(""), row, 0)  # 空标签占位
        layout.addWidget(content_widget, row, 1, 1, 2)

        return advanced_group



    def setup_connections(self):
        """设置信号连接"""
        # 基础配置信号连接
        if hasattr(self, 'message_count_spinbox'):
            self.message_count_spinbox.valueChanged.connect(
                lambda v: self._on_setting_changed("instagram_dm.message_count", v)
            )





        if hasattr(self, 'delay_min_spinbox'):
            self.delay_min_spinbox.valueChanged.connect(
                lambda v: self._on_setting_changed("instagram_dm.delay_min", v)
            )

        if hasattr(self, 'delay_max_spinbox'):
            self.delay_max_spinbox.valueChanged.connect(
                lambda v: self._on_setting_changed("instagram_dm.delay_max", v)
            )

        if hasattr(self, 'message_delay_spinbox'):
            self.message_delay_spinbox.valueChanged.connect(
                lambda v: self._on_setting_changed("instagram_dm.message_delay", v)
            )

        if hasattr(self, 'recall_before_dm_switch'):
            self.recall_before_dm_switch.toggled.connect(
                lambda v: self._on_setting_changed("instagram_dm.recall_before_dm", v)
            )

        # 高级配置信号连接
        if hasattr(self, 'record_file_path_input'):
            self.record_file_path_input.textChanged.connect(
                lambda v: self._on_setting_changed("instagram_dm.record_file_path", v)
            )

        if hasattr(self, 'message_content_1_input'):
            self.message_content_1_input.textChanged.connect(
                lambda: self._on_setting_changed("instagram_dm.message_content_1", self.message_content_1_input.toPlainText())
            )

        if hasattr(self, 'message_content_2_input'):
            self.message_content_2_input.textChanged.connect(
                lambda: self._on_setting_changed("instagram_dm.message_content_2", self.message_content_2_input.toPlainText())
            )

        # 发送模式选择信号连接
        if hasattr(self, 'send_mode_group'):
            self.send_mode_group.buttonClicked.connect(
                lambda button: self._on_setting_changed("instagram_dm.send_mode", self.send_mode_group.id(button))
            )

        # 按钮信号连接
        if hasattr(self, 'browse_file_btn'):
            self.browse_file_btn.clicked.connect(self._on_browse_file)

        if hasattr(self, 'refresh_btn'):
            self.refresh_btn.clicked.connect(self._on_refresh_status)

        if hasattr(self, 'stop_task_btn'):
            self.stop_task_btn.clicked.connect(self._on_stop_task)

    def load_settings(self):
        """
        加载设置值
        ========================================
        功能描述: 从统一配置系统加载Instagram私信参数到UI控件
        加载顺序:
        1. 基础配置：私信数量、延迟设置、消息间隔
        2. 任务控制：私信前撤回开关
        3. 高级配置：记录文件路径、消息内容、发送模式
        注意: 所有配置都从simple_config.py统一管理
        ========================================
        """
        try:
            # 🎯 基础配置加载
            message_count = self.config_manager.get("instagram_dm.message_count", 10)
            self.message_count_spinbox.setValue(message_count)





            delay_min = self.config_manager.get("instagram_dm.delay_min", 5000)
            self.delay_min_spinbox.setValue(delay_min)

            delay_max = self.config_manager.get("instagram_dm.delay_max", 10000)
            self.delay_max_spinbox.setValue(delay_max)

            message_delay = self.config_manager.get("instagram_dm.message_delay", 2000)
            self.message_delay_spinbox.setValue(message_delay)

            recall_before_dm = self.config_manager.get("instagram_dm.recall_before_dm", False)
            self.recall_before_dm_switch.set_checked(recall_before_dm)

            # 加载高级配置
            record_file_path = self.config_manager.get("instagram_dm.record_file_path", "sent_users.txt")
            self.record_file_path_input.setText(record_file_path)

            message_content_1 = self.config_manager.get("instagram_dm.message_content_1", "hi|hello|nice to meet you")
            self.message_content_1_input.setPlainText(message_content_1)

            message_content_2 = self.config_manager.get("instagram_dm.message_content_2", "How are you?|What's up?|Nice day")
            self.message_content_2_input.setPlainText(message_content_2)

            # 加载发送模式设置
            send_mode = self.config_manager.get("instagram_dm.send_mode", 1)  # 默认选择第一个选项
            if hasattr(self, 'send_mode_group'):
                button = self.send_mode_group.button(send_mode)
                if button:
                    button.setChecked(True)

            self.logger.info("Instagram私信配置加载完成")

        except Exception as e:
            self.logger.error(f"加载Instagram私信配置失败: {e}")

    def _on_setting_changed(self, key: str, value):
        """
        设置变更处理
        ========================================
        功能描述: 处理UI控件值变更，保存到统一配置系统
        处理流程:
        1. 保存配置到config_manager（立即持久化到app_config.json）
        2. 发送setting_changed信号通知其他组件
        3. 记录配置变更日志
        参数: key=配置键名, value=新配置值
        ========================================
        """
        try:
            # 🎯 保存到统一配置系统
            self.config_manager.set(key, value)
            # 🎯 发送变更信号
            self.setting_changed.emit(key, value)
            # 🎯 记录变更日志
            self.logger.debug(f"Instagram私信配置更新: {key} = {value}")
        except Exception as e:
            self.logger.error(f"保存Instagram私信配置失败: {e}")

    def _on_browse_file(self):
        """浏览文件"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "选择记录文件", self.record_file_path_input.text(), "文本文件 (*.txt);;所有文件 (*)"
        )
        if file_path:
            self.record_file_path_input.setText(file_path)

    def _on_refresh_status(self):
        """刷新状态"""
        self.logger.info("刷新Instagram私信任务状态")

        # 🎯 寻找主窗口 - 通过父对象链向上查找
        main_window = self._find_main_window()
        if main_window and hasattr(main_window, '_update_instagram_global_summary'):
            self.logger.info("通过主窗口触发全局汇总更新")
            main_window._update_instagram_global_summary()
        else:
            # 🎯 备用方案：直接调用本地更新
            self.logger.info("使用备用方案进行汇总更新")
            self.update_global_summary()

    def _find_main_window(self):
        """🎯 向上查找主窗口对象"""
        current = self
        while current:
            current = current.parent()
            if current and hasattr(current, 'emulator_model') and hasattr(current, '_update_instagram_global_summary'):
                self.logger.info(f"找到主窗口: {type(current)}")
                return current
        self.logger.warning("未找到主窗口")
        return None

    def _on_stop_task(self):
        """停止任务"""
        self.logger.info("请求停止Instagram私信任务")
        self.task_stop_requested.emit()

    def update_task_status(self, completed: int, total: int, success_rate: float, runtime: str):
        """更新任务状态显示 - 已简化，只保留汇总统计"""
        try:
            # 🎯 简化：移除了详细状态卡片，只保留全局汇总统计
            # 如果需要显示详细状态，可以通过日志或其他方式
            self.logger.info(f"任务状态更新: 完成{completed}/{total}, 成功率{success_rate:.1f}%, 运行时间{runtime}")
        except Exception as e:
            self.logger.error(f"更新任务状态显示失败: {e}")

    def update_global_summary(self, table_model=None, force_refresh=False):
        """🎯 更新全局汇总统计 - 使用高性能共享服务"""
        try:
            # 🎯 获取表格模型
            if table_model is None:
                main_window = self._find_main_window()
                if main_window and hasattr(main_window, 'emulator_model'):
                    table_model = main_window.emulator_model
                else:
                    self.logger.warning("无法获取表格模型，跳过汇总统计更新")
                    return

            # 🎯 使用高性能共享服务
            follow_count, dm_count = self._summary_service.get_summary(table_model, force_refresh)

            # 🎯 直接更新UI显示
            self.total_follow_card.update_value(str(follow_count))
            self.total_dm_card.update_value(str(dm_count))

            # 🎯 获取缓存信息用于调试
            cache_info = self._summary_service.get_cache_info()
            if cache_info['cache_valid']:
                self.logger.debug(f"使用缓存数据: 关注{follow_count}人, 私信{dm_count}条")
            elif cache_info['worker_running']:
                self.logger.info(f"异步处理中: 当前显示关注{follow_count}人, 私信{dm_count}条 "
                               f"(阈值{cache_info['async_threshold']}行)")
            else:
                self.logger.info(f"同步处理完成: 关注{follow_count}人, 私信{dm_count}条 "
                               f"(处理{cache_info['processed_count']}行)")

        except Exception as e:
            self.logger.error(f"更新全局汇总统计失败: {e}", exc_info=True)

    def _on_summary_updated(self, follow_count: int, dm_count: int):
        """🎯 响应共享服务的统计更新信号 - 支持异步更新"""
        try:
            self.total_follow_card.update_value(str(follow_count))
            self.total_dm_card.update_value(str(dm_count))
            self.logger.info(f"收到异步汇总更新: 关注{follow_count}人, 私信{dm_count}条")
        except Exception as e:
            self.logger.error(f"处理汇总更新信号失败: {e}")
